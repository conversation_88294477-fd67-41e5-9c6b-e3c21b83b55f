#!/bin/bash

# Test GitHub Actions Workflow Locally
# This script simulates the exact steps from your .github/workflows/1800Accountant.yml

echo "🚀 Starting local workflow simulation..."
echo "================================================"

# Step 1: Checkout Repository (already done since we're in the repo)
echo "✅ Step 1: Repository already checked out"

# Step 2: Set up Node.js (check current version)
echo "📦 Step 2: Checking Node.js version..."
node_version=$(node --version)
echo "Current Node.js version: $node_version"
if [[ $node_version == v18* ]]; then
    echo "✅ Node.js 18 is installed"
else
    echo "⚠️  Warning: Workflow expects Node.js 18, you have $node_version"
fi

# Step 3: Install Dependencies
echo "📦 Step 3: Installing dependencies..."
npm install
if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Step 4: List test files (as per YAML)
echo "📋 Step 4: Listing test files..."
find cypress/e2e -name "*.cy.js" | sort

# Step 5: Run Cypress Tests (exact command from YAML)
echo "🧪 Step 5: Running Cypress tests..."
echo "Command: npx cypress run --browser chrome --spec cypress/e2e/ui/tests/*.cy.js"

# Run the exact same command as in your YAML
npx cypress run --browser chrome --spec "cypress/e2e/ui/tests/*.cy.js"

# Capture exit code
cypress_exit_code=$?

# Step 6: Check results
echo "================================================"
if [ $cypress_exit_code -eq 0 ]; then
    echo "✅ All tests passed! Your workflow should work correctly."
else
    echo "❌ Some tests failed. Exit code: $cypress_exit_code"
    echo "This is what would happen in your GitHub Actions workflow."
fi

# Step 7: Check for reports (as per YAML upload step)
echo "📊 Step 6: Checking for test reports..."
if [ -f "cypress/reports/index.html" ]; then
    echo "✅ Test report found: cypress/reports/index.html"
    echo "In GitHub Actions, this would be uploaded as an artifact"
    echo "You can open it locally: open cypress/reports/index.html"
else
    echo "⚠️  No test report found at cypress/reports/index.html"
fi

echo "================================================"
echo "🏁 Local workflow simulation completed!"
echo "Exit code: $cypress_exit_code"
