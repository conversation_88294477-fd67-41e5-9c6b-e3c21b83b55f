#!/bin/bash

# Test GitHub Actions Workflow Locally
# This script simulates the exact steps from your .github/workflows/1800Accountant.yml

echo "🚀 Starting local workflow simulation..."
echo "================================================"

# Parse command line arguments
DRY_RUN=false
SPEC_PATTERN=""
BROWSER="chrome"

while [[ $# -gt 0 ]]; do
  case $1 in
    --dry-run)
      DRY_RUN=true
      shift
      ;;
    --spec)
      SPEC_PATTERN="$2"
      shift 2
      ;;
    --browser)
      BROWSER="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  --dry-run          Show what would be executed without running tests"
      echo "  --spec PATTERN     Override spec pattern (default: cypress/e2e/ui/tests/*.cy.js)"
      echo "  --browser NAME     Browser to use (default: chrome)"
      echo "  --help             Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Set default spec pattern if not provided
if [ -z "$SPEC_PATTERN" ]; then
    SPEC_PATTERN="cypress/e2e/ui/tests/*.cy.js"
fi

# Step 1: Checkout Repository (already done since we're in the repo)
echo "✅ Step 1: Repository already checked out"

# Step 2: Set up Node.js (check current version)
echo "📦 Step 2: Checking Node.js version..."
node_version=$(node --version)
echo "Current Node.js version: $node_version"
if [[ $node_version == v18* ]]; then
    echo "✅ Node.js 18 is installed"
else
    echo "⚠️  Warning: Workflow expects Node.js 18, you have $node_version"
fi

# Step 3: Install Dependencies
echo "📦 Step 3: Installing dependencies..."
if [ "$DRY_RUN" = true ]; then
    echo "🔍 DRY RUN: Would run 'npm install'"
else
    npm install
    if [ $? -eq 0 ]; then
        echo "✅ Dependencies installed successfully"
    else
        echo "❌ Failed to install dependencies"
        exit 1
    fi
fi

# Step 4: List test files (as per YAML)
echo "📋 Step 4: Listing test files that will be executed..."
test_files=$(find cypress/e2e/ui/tests -name "*.cy.js" | sort)
test_count=$(echo "$test_files" | wc -l)
echo "Found $test_count test files:"
echo "$test_files" | head -10
if [ $test_count -gt 10 ]; then
    echo "... and $((test_count - 10)) more files"
fi

# Step 5: Run Cypress Tests (exact command from YAML)
echo "🧪 Step 5: Running Cypress tests..."
echo "Command: npx cypress run --browser $BROWSER --spec \"$SPEC_PATTERN\""

if [ "$DRY_RUN" = true ]; then
    echo "🔍 DRY RUN: Would execute the above command"
    echo "🔍 This would run $test_count test files"
    cypress_exit_code=0
else
    # Run the exact same command as in your YAML
    npx cypress run --browser "$BROWSER" --spec "$SPEC_PATTERN"
    cypress_exit_code=$?
fi

# Step 6: Check results
echo "================================================"
if [ $cypress_exit_code -eq 0 ]; then
    echo "✅ All tests passed! Your workflow should work correctly."
else
    echo "❌ Some tests failed. Exit code: $cypress_exit_code"
    echo "This is what would happen in your GitHub Actions workflow."
fi

# Step 7: Check for reports (as per YAML upload step)
echo "📊 Step 6: Checking for test reports..."
if [ "$DRY_RUN" = true ]; then
    echo "🔍 DRY RUN: Would check for cypress/reports/index.html"
else
    # Check for any HTML reports
    report_files=$(find cypress/reports -name "*.html" 2>/dev/null)
    if [ -n "$report_files" ]; then
        echo "✅ Test reports found:"
        echo "$report_files"
        echo "In GitHub Actions, these would be uploaded as artifacts"
        echo "You can open them locally:"
        echo "$report_files" | while read -r file; do
            echo "  open $file"
        done
    else
        echo "⚠️  No test reports found in cypress/reports/"
    fi
fi

echo "================================================"
echo "🏁 Local workflow simulation completed!"
echo "Exit code: $cypress_exit_code"

if [ "$DRY_RUN" = true ]; then
    echo ""
    echo "💡 This was a dry run. To actually execute tests, run without --dry-run"
    echo "💡 To run a subset of tests, use: $0 --spec \"cypress/e2e/ui/tests/TC_adminPortalLogin.cy.js\""
fi
