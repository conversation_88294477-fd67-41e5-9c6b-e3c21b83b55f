# Pull Request

## 📋 Description
Brief description of changes made in this PR.

## 🔗 Related Issues
- Fixes #(issue number)
- Closes #(issue number)
- Related to #(issue number)

## 🎯 Type of Change
- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🔧 Refactoring (no functional changes)
- [ ] 🧪 Test improvements
- [ ] 🚀 Performance improvements

## 🧪 Test Changes

### New Tests Added
- [ ] New test file: `TC_[feature].cy.js`
- [ ] New test cases in existing file
- [ ] API test cases
- [ ] Page object methods

### Modified Tests
- [ ] Updated existing test cases
- [ ] Modified page objects
- [ ] Updated test data/fixtures
- [ ] Configuration changes

### Test Coverage
- [ ] Covers happy path scenarios
- [ ] Covers edge cases
- [ ] Covers error scenarios
- [ ] Includes negative testing

## 🔍 Testing Performed

### Local Testing
- [ ] All tests pass locally
- [ ] New tests execute successfully
- [ ] No regression in existing tests
- [ ] Tested in headed mode for debugging

### Environment Testing
- [ ] UAT environment
- [ ] Staging environment
- [ ] Cross-browser testing (if applicable)

## 📸 Screenshots/Videos
If applicable, add screenshots or videos demonstrating the changes.

## 🔧 Configuration Changes
- [ ] Updated `cypress.config.js`
- [ ] Modified `package.json` scripts
- [ ] Changed GitHub Actions workflow
- [ ] Updated environment variables

## 📚 Documentation Updates
- [ ] Updated README.md
- [ ] Updated CONTRIBUTING.md
- [ ] Added inline code comments
- [ ] Updated CHANGELOG.md

## ✅ Checklist

### Code Quality
- [ ] Code follows existing style and conventions
- [ ] Meaningful variable and method names
- [ ] Proper error handling implemented
- [ ] No hard-coded values (use fixtures/config)
- [ ] No sensitive data in code

### Testing
- [ ] All existing tests still pass
- [ ] New tests are independent and atomic
- [ ] Page Object Model pattern followed
- [ ] Test data stored in fixtures
- [ ] Proper waits and assertions used

### Documentation
- [ ] Code is self-documenting or has comments
- [ ] README updated if needed
- [ ] CHANGELOG updated
- [ ] Breaking changes documented

### Security
- [ ] No sensitive information exposed
- [ ] Test credentials are appropriate
- [ ] No production data used in tests

## 🚀 Deployment Notes
Any special deployment considerations or steps required.

## 🔄 Rollback Plan
How to rollback these changes if needed.

## 👥 Reviewers
Please tag relevant team members for review:
- @qa-lead
- @automation-team
- @devops-team (if CI/CD changes)

## 📝 Additional Notes
Any additional information that reviewers should know.

---

## For Reviewers

### Review Checklist
- [ ] Code quality and style
- [ ] Test coverage and effectiveness
- [ ] Documentation completeness
- [ ] Security considerations
- [ ] Performance impact
- [ ] Breaking changes identified

### Testing Instructions
1. Checkout this branch
2. Run `npm install`
3. Execute tests
4. Verify new functionality works as expected

### Questions for Author
- Any specific areas you'd like feedback on?
- Are there any known limitations or trade-offs?
- How does this change affect existing workflows?
