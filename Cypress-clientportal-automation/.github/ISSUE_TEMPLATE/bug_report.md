---
name: Bug Report
about: Create a report to help us improve the test suite
title: '[BUG] '
labels: bug
assignees: ''
---

## 🐛 Bug Description
A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## ✅ Expected Behavior
A clear and concise description of what you expected to happen.

## ❌ Actual Behavior
A clear and concise description of what actually happened.

## 📸 Screenshots/Videos
If applicable, add screenshots or videos to help explain your problem.

## 🖥️ Environment
- **OS**: [e.g. macOS 12.0, Windows 11, Ubuntu 20.04]
- **Browser**: [e.g. Chrome 96, Firefox 95]
- **Node.js Version**: [e.g. 18.12.0]
- **Cypress Version**: [e.g. 14.4.1]
- **Test Environment**: [e.g. UAT, Staging]

## 📋 Test Details
- **Test File**: [e.g. TC_adminPortalLogin.cy.js]
- **Test Case**: [e.g. "should login with valid credentials"]
- **Line Number**: [if applicable]

## 🔍 Error Messages
```
Paste any error messages here
```

## 📊 Test Execution Context
- **Execution Mode**: [e.g. headed, headless, CI/CD]
- **Spec Pattern**: [e.g. single file, multiple files]
- **Browser Mode**: [e.g. local, GitHub Actions]

## 🔧 Attempted Solutions
Describe any solutions you've tried:
- [ ] Cleared browser cache
- [ ] Restarted test execution
- [ ] Updated dependencies
- [ ] Checked network connectivity

## 📎 Additional Context
Add any other context about the problem here.

## 🏷️ Labels
Please add appropriate labels:
- Priority: `low`, `medium`, `high`, `critical`
- Type: `flaky-test`, `environment-issue`, `framework-bug`
- Component: `ui-tests`, `api-tests`, `ci-cd`, `configuration`
