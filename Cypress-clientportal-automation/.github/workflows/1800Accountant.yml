name: Cypress Automation Tests

on:
  push:
    branches:
      - main
  schedule:
    - cron: "0 10 * * *"
  workflow_dispatch:  # Allow manual triggering

jobs:
  cypress:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Cache node modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install Dependencies
        run: npm ci

      - name: List test files
        run: find cypress/e2e -name "*.cy.js" | sort
        
      - name: Run Cypress Tests
        uses: cypress-io/github-action@v6
        with:
          browser: chrome
          headed: false
          spec: "cypress/e2e/all_uat.cy.js"
          # Alternative: Run all tests individually
          # spec: "cypress/e2e/ui/tests/*.cy.js"
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload Cypress test reports
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: cypress-reports-${{ github.run_number }}
          path: |
            cypress/reports/
            cypress/screenshots/
            cypress/videos/
          retention-days: 15

      - name: Upload test results summary
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-summary-${{ github.run_number }}
          path: cypress/reports/index.html
          retention-days: 30