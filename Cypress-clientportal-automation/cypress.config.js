/**
 * Cypress Configuration File
 * Generated by Cypress Bootstrapper AI
 */

const { defineConfig } = require('cypress');

module.exports = defineConfig({
  chromeWebSecurity: false,
  e2e: {
    // Default browser for test execution
    //  browser: 'chrome',

    // Timeout settings
    defaultCommandTimeout: 6000,
    requestTimeout: 6000,
    responseTimeout: 6000,

    // Node events configuration
    setupNodeEvents(on, config) {
      // Configure mochawesome reporter
      require('cypress-mochawesome-reporter/plugin')(on);

      // Browser launch configuration
      on('before:browser:launch', (browser = {}, launchOptions) => {
        if (browser.name === 'chrome' && browser.isHeadless) {
          launchOptions.args.push('--window-size=1920,1080');
        }
        return launchOptions;
      });

      return config;
    },

    env: {
      url: 'https://uat.1800accountant.com'
    },
    testIsolation: false, // For ssession management
    // File patterns and locations
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.js',
    fixturesFolder: 'cypress/fixtures',
    screenshotsFolder: 'cypress/screenshots',
    videosFolder: 'cypress/videos'
  },

  // Reporter configuration
  reporter: 'cypress-mochawesome-reporter',
  reporterOptions: {
    reportDir: 'cypress/reports',
    overwrite: false,
    html: true,
    json: true
  }
});
