/**
 * Cypress Configuration File
 * Generated by Cypress Bootstrapper AI
 */

const { defineConfig } = require('cypress');

module.exports = defineConfig({
  chromeWebSecurity: false,
  e2e: {
    // Default browser for test execution
    //  browser: 'chrome',

    // Timeout settings - increased for better stability
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    pageLoadTimeout: 30000,

    // Retry configuration
    retries: {
      runMode: 2,
      openMode: 0
    },

    // Node events configuration
    setupNodeEvents(on, config) {
      // Configure mochawesome reporter
      require('cypress-mochawesome-reporter/plugin')(on);

      // Browser launch configuration
      on('before:browser:launch', (browser = {}, launchOptions) => {
        if (browser.name === 'chrome' && browser.isHeadless) {
          launchOptions.args.push('--window-size=1920,1080');
          launchOptions.args.push('--disable-dev-shm-usage');
          launchOptions.args.push('--no-sandbox');
        }
        return launchOptions;
      });

      // Add file system tasks
      on('task', {
        fileExists(filePath) {
          const fs = require('fs');
          return fs.existsSync(filePath);
        },
        log(message) {
          console.log(message);
          return null;
        }
      });

      return config;
    },

    env: {
      // Environment URLs
      url: 'https://uat.1800accountant.com',
      stageUrl: 'https://staging.1800accountant.com',
      adminUrl: 'https://staging.1800accountant.com/cbapi/app.php/accountant_login',

      // Test configuration
      defaultTimeout: 10000,
      apiTimeout: 30000,

      // Feature flags
      skipRecaptcha: true,
      enableScreenshots: true
    },
    testIsolation: false, // For ssession management
    // File patterns and locations
    specPattern: 'cypress/e2e/all*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.js',
    fixturesFolder: 'cypress/fixtures',
    screenshotsFolder: 'cypress/screenshots',
    videosFolder: 'cypress/videos'
  },

  // Reporter configuration
  reporter: 'cypress-mochawesome-reporter',
  reporterOptions: {
    reportDir: 'cypress/reports',
    overwrite: false,
    html: true,
    json: true
  }
});
