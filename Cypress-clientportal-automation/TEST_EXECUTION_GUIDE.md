# Test Execution Guide

## Issue Resolution Summary

### Problem Identified
Your workflow was only running 2 test cases out of 38+ available test files because:

1. **Limited Test Imports**: The main test files (`all_uat.cy.js` and `all_stage.cy.js`) only imported 2 test files each
2. **Commented Out Tests**: Many test imports were commented out in the stage file
3. **Workflow Configuration**: The GitHub Actions workflow was correctly configured but limited by the test file imports

### Solution Implemented
✅ **Updated `all_uat.cy.js`** - Now imports all 38+ test files organized by category
✅ **Updated `all_stage.cy.js`** - Now imports all 38+ test files organized by category  
✅ **Added comprehensive npm scripts** - Multiple ways to run tests
✅ **Created test runner utility** - `run-all-tests.js` for advanced test execution
✅ **Updated package.json** - Added new test scripts

## Available Test Files (38+ Tests)

### Core Login & Navigation (5 tests)
- TC_adminPortalLogin.cy.js
- TC_adminPortalLoginStage.cy.js
- TC_contactScreenAllTabNavigation.cy.js
- TC_clientPortalLoginUsingAdminUser.cy.js
- TC_allAdminPageNavigations.cy.js
- TC_mainNavigation.cy.js

### Account & Contact Management (4 tests)
- TC_addEditDeleteContacts.cy.js
- TC_editContact.cy.js
- TC_adminContactAndAccountSearch.cy.js
- TC_addNewBusinessForanExistingUser.cy.js

### Financial Management (7 tests)
- TC_addEditDeleteTransactions.cy.js
- TC_addEditMoveDeleteCategory.cy.js
- TC_AddUpdateDeleteInvoices.cy.js
- TC_addConnectedBankAccount.cy.js
- TC_addManualBankAccount.cy.js
- TC_reconciliations.cy.js
- TC_submitUnpublishEditDeleteReconciliations.cy.js

### Products & Services (1 test)
- TC_addEditDeleteProductsServices.cy.js

### Reporting (1 test)
- TC_generateAllReports.cy.js

### Journal & Accounting (1 test)
- TC_journalEntry.cy.js

### Contractor & 1099 (1 test)
- TC_contractorCreationAnd1099Filing.cy.js

### Payroll (3 tests)
- TC_payrollSetup.cy.js
- TC_PayrollSetupADP.cy.js
- TC_Payroll_Appointment_schedule_Using_TeamModule.cy.js

### Tax Center (1 test)
- TC_taxCenterPageAllFunctionality.cy.js

### Document Management (3 tests)
- TC_documentsUpload.cy.js
- TC_documentUploadInMessage.cy.js
- TC_receiptScan.cy.js

### Communication (4 tests)
- TC_startNewConversation.cy.js
- TC_replyToAnExistingMassageThreadFromClientSide.cy.js
- TC_viewArchivedMesg.cy.js
- TC_emailer.cy.js

### Mileage & Expenses (1 test)
- TC_mileageLog.cy.js

### VTO Tests (2 tests)
- TC_businessVto.cy.js
- TC_personalVto.cy.js

### Additional Functionality (3 tests)
- TC_additionalProduct.cy.js
- TC_upgradeClientTieredPermission.cy.js
- TC_accountantCreation.cy.js

### AP Creation (1 test)
- TC_APcreationOnClientCenterPage.cy.js

### User Management (4 tests)
- TC_createAccount.cy.js
- TC_joinnowPage.cy.js
- TC_signUp.cy.js
- TC_signin.cy.js

## How to Run Tests

### 1. Using NPM Scripts (Recommended)

```bash
# Run all UAT tests (headless)
npm run test:all:uat

# Run all Stage tests (headless)
npm run test:all:stage

# Run all UAT tests (with browser)
npm run test:all:uat:headed

# Run all Stage tests (with browser)
npm run test:all:stage:headed

# Open Cypress Test Runner
npm run test:open

# List all available test files
npm run test:list

# Get help
npm run test:help
```

### 2. Using Custom Test Runner

```bash
# List all test files
node run-all-tests.js list

# Run all tests for UAT environment
node run-all-tests.js run uat

# Run all tests for Stage environment with browser
node run-all-tests.js run stage headed

# Run individual test file
node run-all-tests.js run-individual cypress/e2e/ui/tests/TC_adminPortalLogin.cy.js

# Run individual test with browser
node run-all-tests.js run-individual cypress/e2e/ui/tests/TC_adminPortalLogin.cy.js headed
```

### 3. Direct Cypress Commands

```bash
# Run specific test file
npx cypress run --spec "cypress/e2e/ui/tests/TC_adminPortalLogin.cy.js"

# Run all tests in a directory
npx cypress run --spec "cypress/e2e/ui/tests/*.cy.js"

# Run with specific browser
npx cypress run --spec "cypress/e2e/all_uat.cy.js" --browser chrome --headed
```

## GitHub Actions Workflow

Your workflow will now run all 38+ test cases when triggered because:
- The `all_uat.cy.js` file now imports all test files
- The workflow runs `cypress/e2e/all_uat.cy.js` which includes everything

## Next Steps

1. **Test the Changes**: Run `npm run test:all:uat` locally to verify all tests execute
2. **Check Dependencies**: Ensure all test files have their required fixtures and page objects
3. **Review Test Order**: Some tests may depend on others - review the import order if needed
4. **Monitor Execution Time**: With 38+ tests, execution time will be longer
5. **Consider Parallel Execution**: For faster CI/CD, consider running tests in parallel

## Troubleshooting

If some tests fail:
1. Check if required fixture files exist
2. Verify page object classes are properly implemented
3. Ensure test dependencies are met (login sessions, etc.)
4. Review test isolation settings in `cypress.config.js`
