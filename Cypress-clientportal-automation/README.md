# 1800Accountant E2E Test Suite

[![Cypress Tests](https://github.com/your-org/1800-QA/actions/workflows/1800Accountant.yml/badge.svg)](https://github.com/your-org/1800-QA/actions/workflows/1800Accountant.yml)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen.svg)](https://nodejs.org/)
[![Cypress Version](https://img.shields.io/badge/cypress-14.4.1-green.svg)](https://cypress.io/)

> Comprehensive end-to-end test automation suite for the 1800Accountant client portal using Cypress framework.

## 📋 Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Usage](#usage)
- [Project Structure](#project-structure)
- [Test Execution](#test-execution)
- [Configuration](#configuration)
- [CI/CD Integration](#cicd-integration)
- [Contributing](#contributing)
- [Troubleshooting](#troubleshooting)
- [Support](#support)

## 🎯 Overview

This repository contains automated end-to-end tests for the 1800Accountant platform, covering:

- **Admin Portal** - Administrative functions and user management
- **Client Portal** - Client-facing features and workflows
- **API Testing** - Backend service validation
- **Cross-browser Testing** - Chrome, Firefox, Edge compatibility
- **Mobile Responsive** - Mobile and tablet view testing

### Test Coverage
- 🔐 **Authentication & Authorization** - Login, logout, session management
- 👥 **User Management** - Account creation, profile management
- 💼 **Business Operations** - Bookkeeping, payroll, tax center
- 📊 **Reporting** - Financial reports and analytics
- 📄 **Document Management** - Upload, download, processing
- 💬 **Communication** - Messaging, notifications

## ✨ Features

- **Page Object Model** - Maintainable and reusable test architecture
- **Data-Driven Testing** - JSON fixtures for test data management
- **Cross-Environment Support** - UAT, Staging, Production configurations
- **Comprehensive Reporting** - HTML reports with screenshots and videos
- **CI/CD Integration** - GitHub Actions automated testing
- **Session Management** - Efficient test execution with session reuse
- **API Testing** - Backend validation alongside UI tests

## 🔧 Prerequisites

Before running the tests, ensure you have:

- **Node.js** >= 18.0.0 ([Download](https://nodejs.org/))
- **npm** >= 8.0.0 (comes with Node.js)
- **Git** for version control
- **Chrome Browser** (for headless execution)

### System Requirements
- **OS**: Windows 10+, macOS 10.15+, or Linux Ubuntu 18.04+
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: 2GB free space for dependencies and reports

## 🚀 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/1800-QA.git
   cd Cypress-clientportal-automation
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Verify installation**
   ```bash
   npx cypress verify
   ```

## 📖 Usage

### Quick Start

```bash
# Run UAT smoke tests (recommended for quick validation)
npm run uatSanity

# Run staging smoke tests
npm run stageSanity

# Run with browser visible (for debugging)
npm run uatSanityOnBrowser
```

### Test Execution Options

```bash
# Open Cypress Test Runner (interactive mode)
npx cypress open

# Run specific test file
npx cypress run --spec "cypress/e2e/ui/tests/TC_adminPortalLogin.cy.js"

# Run tests with specific browser
npx cypress run --browser firefox

# Run tests in headed mode (visible browser)
npx cypress run --headed
```

## 📁 Project Structure

```
Cypress-clientportal-automation/
├── .github/
│   └── workflows/
│       └── 1800Accountant.yml      # GitHub Actions CI/CD pipeline
├── cypress/
│   ├── e2e/
│   │   ├── all_uat.cy.js           # UAT environment smoke tests
│   │   ├── all_stage.cy.js         # Staging environment smoke tests
│   │   ├── api/                    # API test files
│   │   │   ├── 1-loginAPI.cy.js
│   │   │   ├── 2-current-loggedIn-userAPI.cy.js
│   │   │   └── 3-user-permissionAPI.cy.js
│   │   └── ui/
│   │       ├── pages/              # Page Object Models
│   │       │   ├── adminPortalLogin.js
│   │       │   ├── contactsPage.js
│   │       │   ├── transactionsPage.js
│   │       │   └── ...
│   │       └── tests/              # UI test specifications
│   │           ├── TC_adminPortalLogin.cy.js
│   │           ├── TC_contactScreenAllTabNavigation.cy.js
│   │           └── ...
│   ├── fixtures/                   # Test data files
│   │   ├── credentials.json
│   │   ├── testUser.json
│   │   ├── PayrollData.json
│   │   └── images/
│   ├── support/
│   │   ├── commands.js             # Custom Cypress commands
│   │   └── e2e.js                  # Global configuration
│   └── reports/                    # Generated test reports
├── cypress.config.js               # Cypress configuration
├── package.json                    # Dependencies and scripts
└── README.md                       # This file
```

### Key Directories

- **`cypress/e2e/ui/pages/`** - Page Object Models following best practices
- **`cypress/e2e/ui/tests/`** - Test specifications organized by functionality
- **`cypress/fixtures/`** - Test data and configuration files
- **`cypress/support/`** - Custom commands and global configurations

## 🧪 Test Execution

### Environment-Specific Testing

#### UAT Environment
```bash
# Smoke tests (2 critical test cases)
npm run uatSanity

# With browser visible
npm run uatSanityOnBrowser
```

#### Staging Environment
```bash
# Smoke tests (2 critical test cases)
npm run stageSanity

# With browser visible
npm run stageSanityOnBrowser
```

### Test Categories

| Category | Description | Test Count | Execution Time |
|----------|-------------|------------|----------------|
| **Smoke Tests** | Critical path validation | 2 tests | ~2 minutes |
| **Full UI Suite** | Complete UI functionality | 43 tests | ~21 minutes |
| **API Tests** | Backend service validation | 3 tests | ~1 minute |

### Running Specific Test Suites

```bash
# Run all UI tests
npx cypress run --spec "cypress/e2e/ui/tests/*.cy.js"

# Run all API tests
npx cypress run --spec "cypress/e2e/api/*.cy.js"

# Run tests by pattern
npx cypress run --spec "cypress/e2e/ui/tests/TC_admin*.cy.js"

# Run single test file
npx cypress run --spec "cypress/e2e/ui/tests/TC_adminPortalLogin.cy.js"
```

## ⚙️ Configuration

### Environment Variables

Configure different environments in `cypress.config.js`:

```javascript
env: {
  url: 'https://uat.1800accountant.com'  // Default UAT environment
}
```

### Test Configuration

Key configuration options:

- **Timeouts**: 6 seconds default command timeout
- **Test Isolation**: Disabled for session management
- **Browser**: Chrome (headless in CI, headed for local debugging)
- **Retries**: Configured for CI stability

### Custom Commands

Available custom commands in `cypress/support/commands.js`:

- `cy.waitForPageLoad()` - Enhanced page load waiting
- `cy.setupAdminSession()` - Admin session management
- `cy.saveLocalStorage()` / `cy.restoreLocalStorage()` - Session persistence

## 🔄 CI/CD Integration

### GitHub Actions Workflow

The project includes automated testing via GitHub Actions:

**Triggers:**
- Push to `main` branch
- Daily at 10:00 AM UTC
- Manual workflow dispatch

**Workflow Steps:**
1. Checkout code
2. Setup Node.js 18
3. Install dependencies
4. Run Cypress tests
5. Upload test reports as artifacts

### Workflow Configuration

```yaml
# .github/workflows/1800Accountant.yml
- name: Run Cypress Tests
  uses: cypress-io/github-action@v6
  with:
    browser: chrome
    headed: false
    spec: "cypress/e2e/ui/tests/*.cy.js"  # Runs all 43 UI tests
```

### Artifacts

After each workflow run:
- **Test Reports** - HTML reports with detailed results
- **Screenshots** - Captured on test failures
- **Videos** - Full test execution recordings (if enabled)

## 🤝 Contributing

### Development Workflow

1. **Create a feature branch**
   ```bash
   git checkout -b feature/new-test-case
   ```

2. **Write tests following the existing patterns**
   - Use Page Object Model
   - Add test data to fixtures
   - Follow naming conventions

3. **Test locally**
   ```bash
   npm run uatSanity  # Quick validation
   ```

4. **Commit and push**
   ```bash
   git add .
   git commit -m "Add: New test case for feature X"
   git push origin feature/new-test-case
   ```

5. **Create Pull Request**

### Coding Standards

- **File Naming**: Use descriptive names with `TC_` prefix for test files
- **Page Objects**: One class per page, descriptive method names
- **Test Data**: Store in fixtures, use environment-specific data
- **Comments**: Document complex logic and business rules

### Best Practices

- ✅ Keep tests independent and atomic
- ✅ Use data-driven testing with fixtures
- ✅ Implement proper waits and assertions
- ✅ Follow Page Object Model pattern
- ✅ Add meaningful test descriptions
- ❌ Avoid hard-coded waits (`cy.wait(5000)`)
- ❌ Don't chain too many actions in one test
- ❌ Avoid testing third-party functionality

## 🐛 Troubleshooting

### Common Issues

#### 1. Test Timeouts
**Problem**: Tests failing due to timeouts
```bash
Error: Timed out retrying after 6000ms
```
**Solution**:
- Check network connectivity
- Increase timeout in `cypress.config.js`
- Use `cy.waitForPageLoad()` for better waiting

#### 2. Element Not Found
**Problem**: Elements not found during test execution
```bash
Error: Timed out retrying after 6000ms: Expected to find element
```
**Solution**:
- Verify element selectors are correct
- Check if page has fully loaded
- Use more specific selectors or data attributes

#### 3. Session Issues
**Problem**: Tests failing due to authentication
```bash
Error: User not authenticated
```
**Solution**:
- Check fixture files for correct credentials
- Verify `cy.setupAdminSession()` is working
- Clear browser data and retry

#### 4. CI/CD Failures
**Problem**: Tests pass locally but fail in GitHub Actions
**Solution**:
- Check Node.js version compatibility (use 18+)
- Verify all dependencies are in `package.json`
- Review GitHub Actions logs for specific errors

### Debug Mode

```bash
# Run with browser visible for debugging
npm run uatSanityOnBrowser

# Run single test with debug output
npx cypress run --spec "cypress/e2e/ui/tests/TC_adminPortalLogin.cy.js" --headed

# Open Cypress Test Runner for interactive debugging
npx cypress open
```

### Performance Optimization

- **Parallel Execution**: Consider running tests in parallel for faster CI
- **Test Isolation**: Currently disabled for session management
- **Selective Testing**: Run only affected tests for faster feedback

## 📊 Test Reports

### HTML Reports

After test execution, reports are generated in:
- **Location**: `cypress/reports/index.html`
- **Content**: Test results, screenshots, execution time
- **Access**: Open in browser or download from GitHub Actions artifacts

### Screenshots and Videos

- **Screenshots**: Captured automatically on test failures
- **Videos**: Full test execution (configurable)
- **Location**: `cypress/screenshots/` and `cypress/videos/`

## 🔐 Security

### Credentials Management

- **Fixtures**: Store test credentials in `cypress/fixtures/`
- **Environment Variables**: Use for sensitive data in CI/CD
- **GitHub Secrets**: Configure `CYPRESS_RECORD_KEY` for cloud recording

### Best Practices

- ✅ Use test-specific accounts, not production data
- ✅ Rotate test credentials regularly
- ✅ Store sensitive data in environment variables
- ❌ Never commit real credentials to repository

## 📈 Monitoring & Metrics

### Key Metrics

- **Test Execution Time**: ~21 minutes for full suite
- **Success Rate**: Monitor in GitHub Actions
- **Flaky Tests**: Track and fix unstable tests
- **Coverage**: 43 UI tests + 3 API tests

### Alerts

- **GitHub Actions**: Email notifications on workflow failures
- **Slack Integration**: Configure for team notifications (optional)

## 📞 Support

### Getting Help

1. **Check Documentation**: Review this README and inline comments
2. **Search Issues**: Look for similar problems in repository issues
3. **Debug Locally**: Use `npx cypress open` for interactive debugging
4. **Contact Team**: Reach out to QA team for assistance

### Useful Resources

- [Cypress Documentation](https://docs.cypress.io/)
- [Page Object Model Best Practices](https://docs.cypress.io/guides/references/best-practices)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)

### Team Contacts

- **QA Lead**: [Contact Information]
- **DevOps**: [Contact Information]
- **Development Team**: [Contact Information]

---

## 📄 License

This project is proprietary software for 1800Accountant internal use only.

## 🏷️ Version History

| Version | Date | Changes |
|---------|------|---------|
| 1.0.0 | 2024-01-01 | Initial test suite setup |
| 1.1.0 | 2024-02-01 | Added API testing |
| 1.2.0 | 2024-03-01 | Enhanced CI/CD integration |

---

**Made with ❤️ by the 1800Accountant QA Team**
