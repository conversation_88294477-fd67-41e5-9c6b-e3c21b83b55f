# 1800Accountant Cypress Test Suite

## Overview
Comprehensive E2E test suite for the 1800Accountant portal using Cypress framework.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation
```bash
npm install
```

### Running Tests

#### Smoke Tests (Recommended for CI/CD)
```bash
# UAT Environment
npm run uatSanity

# Stage Environment
npm run stageSanity

# With Browser (for debugging)
npm run uatSanityOnBrowser
npm run stageSanityOnBrowser
```

#### Full Test Suite
```bash
# All UI tests
npm run test:ui

# All API tests
npm run test:api

# Open Cypress Test Runner
npm run test:open
```

#### Utility Commands
```bash
# Clean reports before running
npm run clean:reports

# Run specific test file
npx cypress run --spec "cypress/e2e/ui/tests/TC_adminPortalLogin.cy.js"
```

## 📁 Project Structure

```
cypress/
├── e2e/
│   ├── all_uat.cy.js          # UAT smoke tests
│   ├── all_stage.cy.js        # Stage smoke tests
│   ├── api/                   # API test files
│   └── ui/
│       ├── pages/             # Page Object Models
│       └── tests/             # UI test files
├── fixtures/                  # Test data files
├── support/
│   ├── commands.js           # Custom commands
│   ├── fixture-helper.js     # Fixture utilities
│   └── e2e.js               # Global configuration
└── reports/                  # Test reports (generated)
```

## 🔧 Configuration

### Environment Variables
Set in `cypress.config.js` or via environment:
- `url`: UAT environment URL
- `stageUrl`: Staging environment URL
- `adminUrl`: Admin login API URL

### Custom Commands
- `cy.waitForPageLoad()` - Enhanced page load waiting
- `cy.safeClick(selector)` - Safe click with retries
- `cy.waitForElement(selector)` - Smart element waiting
- `cy.loadFixture(name, env)` - Environment-specific fixtures
- `cy.generateTestData(type)` - Generate unique test data

## 📊 Test Reports
- HTML reports: `cypress/reports/index.html`
- Screenshots: `cypress/screenshots/`
- Videos: `cypress/videos/`

## 🔄 CI/CD Integration
GitHub Actions workflow runs automatically on:
- Push to main branch
- Daily at 10 AM UTC
- Manual trigger via workflow_dispatch

## 🐛 Troubleshooting

### Common Issues
1. **Timeouts**: Increase timeout values in `cypress.config.js`
2. **Element not found**: Use `cy.waitForElement()` instead of `cy.get()`
3. **Session issues**: Check fixture files and credentials

### Debug Mode
```bash
# Run with browser visible
npm run uatSanityOnBrowser

# Open Cypress Test Runner
npm run test:open
```

## 📝 Writing Tests

### Best Practices
1. Use Page Object Model pattern
2. Store test data in fixtures
3. Use custom commands for common actions
4. Add proper waits and assertions
5. Keep tests independent and atomic

### Example Test Structure
```javascript
import PageObject from '../pages/PageObject';

describe('Feature Tests', () => {
  before(() => {
    cy.fixture('testData').then(data => {
      this.testData = data;
    });
  });

  it('should perform action', function() {
    const page = new PageObject();
    page.performAction(this.testData);
    // assertions
  });
});
```

## 🤝 Contributing
1. Follow existing code patterns
2. Add tests for new features
3. Update documentation
4. Ensure all tests pass before PR

## 📞 Support
For issues or questions, please check the troubleshooting section or contact the QA team.
