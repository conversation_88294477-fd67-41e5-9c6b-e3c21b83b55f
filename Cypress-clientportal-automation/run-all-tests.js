#!/usr/bin/env node

/**
 * Comprehensive Test Runner Script
 * This script helps run all available test cases
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const TEST_DIR = 'cypress/e2e/ui/tests';
const ENVIRONMENTS = {
  uat: 'cypress/e2e/all_uat.cy.js',
  stage: 'cypress/e2e/all_stage.cy.js'
};

// Get all test files
function getAllTestFiles() {
  const testFiles = fs.readdirSync(TEST_DIR)
    .filter(file => file.endsWith('.cy.js'))
    .map(file => path.join(TEST_DIR, file));
  
  console.log(`Found ${testFiles.length} test files:`);
  testFiles.forEach((file, index) => {
    console.log(`  ${index + 1}. ${file}`);
  });
  
  return testFiles;
}

// Run tests based on environment
function runTests(environment = 'uat', mode = 'headless') {
  const specFile = ENVIRONMENTS[environment];
  
  if (!specFile) {
    console.error(`Invalid environment: ${environment}. Use 'uat' or 'stage'`);
    process.exit(1);
  }
  
  const headedFlag = mode === 'headed' ? '--headed' : '';
  const command = `npx cypress run --spec "${specFile}" ${headedFlag}`;
  
  console.log(`Running tests for ${environment} environment...`);
  console.log(`Command: ${command}`);
  
  try {
    execSync(command, { stdio: 'inherit' });
    console.log('✅ All tests completed successfully!');
  } catch (error) {
    console.error('❌ Some tests failed or encountered errors');
    process.exit(1);
  }
}

// Run individual test file
function runIndividualTest(testFile, mode = 'headless') {
  const headedFlag = mode === 'headed' ? '--headed' : '';
  const command = `npx cypress run --spec "${testFile}" ${headedFlag}`;
  
  console.log(`Running individual test: ${testFile}`);
  console.log(`Command: ${command}`);
  
  try {
    execSync(command, { stdio: 'inherit' });
    console.log('✅ Test completed successfully!');
  } catch (error) {
    console.error('❌ Test failed or encountered errors');
    process.exit(1);
  }
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'list':
      getAllTestFiles();
      break;
      
    case 'run':
      const environment = args[1] || 'uat';
      const mode = args[2] || 'headless';
      runTests(environment, mode);
      break;
      
    case 'run-individual':
      const testFile = args[1];
      const testMode = args[2] || 'headless';
      if (!testFile) {
        console.error('Please specify a test file to run');
        console.log('Usage: node run-all-tests.js run-individual <test-file> [headed|headless]');
        process.exit(1);
      }
      runIndividualTest(testFile, testMode);
      break;
      
    case 'help':
    default:
      console.log('Cypress Test Runner');
      console.log('');
      console.log('Usage:');
      console.log('  node run-all-tests.js list                           - List all available test files');
      console.log('  node run-all-tests.js run [uat|stage] [headed]       - Run all tests for environment');
      console.log('  node run-all-tests.js run-individual <file> [headed] - Run specific test file');
      console.log('  node run-all-tests.js help                           - Show this help');
      console.log('');
      console.log('Examples:');
      console.log('  node run-all-tests.js list');
      console.log('  node run-all-tests.js run uat');
      console.log('  node run-all-tests.js run stage headed');
      console.log('  node run-all-tests.js run-individual cypress/e2e/ui/tests/TC_adminPortalLogin.cy.js');
      break;
  }
}

main();
