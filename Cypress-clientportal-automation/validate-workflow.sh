#!/bin/bash

# Pre-commit Workflow Validation Script
# Run this before committing to ensure your workflow will work in GitHub Actions

set -e  # Exit on any error

echo "🔍 Pre-commit Workflow Validation"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "success")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "info")
            echo -e "ℹ️  $message"
            ;;
    esac
}

# Check if we're in the right directory
if [ ! -f "cypress.config.js" ]; then
    print_status "error" "Not in Cypress project directory. Please run from project root."
    exit 1
fi

print_status "info" "Validating GitHub Actions workflow configuration..."

# 1. Check Node.js version
print_status "info" "Checking Node.js version..."
node_version=$(node --version)
if [[ $node_version == v18* ]] || [[ $node_version == v20* ]]; then
    print_status "success" "Node.js version $node_version is compatible"
else
    print_status "warning" "Node.js version $node_version may not match GitHub Actions (expects v18+)"
fi

# 2. Validate package.json and dependencies
print_status "info" "Validating package.json..."
if [ -f "package.json" ]; then
    print_status "success" "package.json found"
    
    # Check if package-lock.json exists
    if [ -f "package-lock.json" ]; then
        print_status "success" "package-lock.json found (good for reproducible builds)"
    else
        print_status "warning" "package-lock.json not found. Consider running 'npm install' to generate it."
    fi
else
    print_status "error" "package.json not found"
    exit 1
fi

# 3. Check Cypress configuration
print_status "info" "Validating Cypress configuration..."
if [ -f "cypress.config.js" ]; then
    print_status "success" "cypress.config.js found"
    
    # Check spec pattern
    spec_pattern=$(grep -o "specPattern.*" cypress.config.js | head -1)
    print_status "info" "Spec pattern: $spec_pattern"
else
    print_status "error" "cypress.config.js not found"
    exit 1
fi

# 4. Count test files that will be executed
print_status "info" "Counting test files that will be executed..."
test_files=$(find cypress/e2e/ui/tests -name "*.cy.js" 2>/dev/null)
test_count=$(echo "$test_files" | grep -c . || echo "0")

if [ $test_count -eq 0 ]; then
    print_status "error" "No test files found in cypress/e2e/ui/tests/"
    exit 1
else
    print_status "success" "Found $test_count test files"
    if [ $test_count -gt 20 ]; then
        print_status "warning" "Large number of tests ($test_count) - workflow may take a long time"
    fi
fi

# 5. Check workflow file syntax
print_status "info" "Validating workflow YAML syntax..."
workflow_file=".github/workflows/1800Accountant.yml"
if [ -f "$workflow_file" ]; then
    # Basic YAML syntax check (if yq is available)
    if command -v yq &> /dev/null; then
        if yq eval . "$workflow_file" > /dev/null 2>&1; then
            print_status "success" "Workflow YAML syntax is valid"
        else
            print_status "error" "Workflow YAML syntax is invalid"
            exit 1
        fi
    else
        print_status "info" "yq not available, skipping YAML syntax validation"
    fi
    
    # Check spec pattern in workflow
    workflow_spec=$(grep -o "spec:.*" "$workflow_file" | head -1)
    print_status "info" "Workflow spec pattern: $workflow_spec"
else
    print_status "error" "Workflow file not found: $workflow_file"
    exit 1
fi

# 6. Test dependency installation
print_status "info" "Testing dependency installation..."
if npm ci --dry-run > /dev/null 2>&1; then
    print_status "success" "Dependencies can be installed successfully"
else
    print_status "warning" "Dependency installation may have issues"
fi

# 7. Quick smoke test (optional)
read -p "🤔 Run a quick smoke test? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "info" "Running smoke test with 1 test file..."
    first_test=$(echo "$test_files" | head -1)
    if [ -n "$first_test" ]; then
        print_status "info" "Testing with: $first_test"
        if npx cypress run --spec "$first_test" --browser chrome > /dev/null 2>&1; then
            print_status "success" "Smoke test passed"
        else
            print_status "warning" "Smoke test failed - check your test setup"
        fi
    fi
fi

# 8. Estimate workflow execution time
print_status "info" "Estimating workflow execution time..."
estimated_time=$((test_count * 30))  # Rough estimate: 30 seconds per test
if [ $estimated_time -gt 1800 ]; then  # 30 minutes
    print_status "warning" "Estimated execution time: ~$((estimated_time / 60)) minutes (may exceed GitHub Actions limits)"
else
    print_status "info" "Estimated execution time: ~$((estimated_time / 60)) minutes"
fi

echo ""
echo "=================================="
print_status "success" "Workflow validation completed!"
echo ""
print_status "info" "Summary:"
echo "  • $test_count test files will be executed"
echo "  • Estimated runtime: ~$((estimated_time / 60)) minutes"
echo "  • Workflow file: $workflow_file"
echo ""
print_status "info" "To test locally before committing:"
echo "  ./test-workflow-locally.sh --dry-run"
echo "  ./test-workflow-locally.sh --spec \"cypress/e2e/ui/tests/TC_adminPortalLogin.cy.js\""
echo ""
print_status "success" "Ready to commit! 🚀"
