/**
 * Cypress E2E Support Configuration
 * Generated by Cypress Bootstrapper AI
 */

// Import API testing plugin
import 'cypress-plugin-api';

// Import XPath support
import 'cypress-xpath';

// Import custom commands
import './commands.js';

// Import test reporting
import 'cypress-mochawesome-reporter/register';

// Import and configure JSON schema validation
import chaiJsonSchema from 'chai-json-schema';
chai.use(chaiJsonSchema);

Cypress.on('uncaught:exception', (err, runnable) => {
  // returning false here prevents Cy<PERSON> from
  // failing the test
  return false
})

let LOCAL_STORAGE_MEMORY = {};

Cypress.Commands.add("saveLocalStorage", () => {
  Object.keys(localStorage).forEach(key => {
    LOCAL_STORAGE_MEMORY[key] = localStorage[key];
  });
});

Cypress.Commands.add("restoreLocalStorage", () => {
  Object.keys(LOCAL_STORAGE_MEMORY).forEach(key => {
    localStorage.setItem(key, LOCAL_STORAGE_MEMORY[key]);
  });
});

afterEach(() => {
  cy.saveLocalStorage();
});





