/**
 * Cypress E2E Support Configuration
 * Generated by Cypress Bootstrapper AI
 */

// Import API testing plugin
import 'cypress-plugin-api';

// Import XPath support
import 'cypress-xpath';

// Import custom commands
import './commands.js';
import './fixture-helper.js';

// Import test reporting
import 'cypress-mochawesome-reporter/register';

// Import and configure JSON schema validation
import chaiJsonSchema from 'chai-json-schema';
chai.use(chaiJsonSchema);

// Enhanced error handling with logging
Cypress.on('uncaught:exception', (err, runnable) => {
  // Log the error for debugging but don't fail the test
  console.log('Uncaught exception:', err.message);

  // Don't fail on specific known errors
  const ignoredErrors = [
    'ResizeObserver loop limit exceeded',
    'Non-Error promise rejection captured',
    'Script error',
    'Network request failed'
  ];

  const shouldIgnore = ignoredErrors.some(ignoredError =>
    err.message.includes(ignoredError)
  );

  if (shouldIgnore) {
    console.log('Ignoring known error:', err.message);
    return false;
  }

  // Log but don't fail for now - can be changed to true for stricter testing
  console.warn('Uncaught exception occurred:', err.message);
  return false;
})

let LOCAL_STORAGE_MEMORY = {};

Cypress.Commands.add("saveLocalStorage", () => {
  Object.keys(localStorage).forEach(key => {
    LOCAL_STORAGE_MEMORY[key] = localStorage[key];
  });
});

Cypress.Commands.add("restoreLocalStorage", () => {
  Object.keys(LOCAL_STORAGE_MEMORY).forEach(key => {
    localStorage.setItem(key, LOCAL_STORAGE_MEMORY[key]);
  });
});

afterEach(() => {
  cy.saveLocalStorage();
});





