

/**
 * Enhanced custom command for waiting for page load
 * Usage: cy.waitForPageLoad()
 */
Cypress.Commands.add('waitForPageLoad', () => {
  // Wait for loading indicators to disappear
  cy.get('[data-testid="loading"]', { timeout: 15000 }).should('not.exist');
  cy.get('.loading, .spinner', { timeout: 5000 }).should('not.exist');

  // Wait for main content to be visible
  cy.get('main, [role="main"], #main-content, body', { timeout: 15000 }).should('be.visible');

  // Wait for network requests to complete
  cy.window().its('document.readyState').should('equal', 'complete');
});

/**
 * Smart wait for element with retry logic
 * Usage: cy.waitForElement('selector', { timeout: 10000, retries: 3 })
 */
Cypress.Commands.add('waitForElement', (selector, options = {}) => {
  const { timeout = 10000, retries = 2 } = options;

  const attemptFind = (attempt = 0) => {
    return cy.get('body').then($body => {
      if ($body.find(selector).length > 0) {
        return cy.get(selector, { timeout });
      } else if (attempt < retries) {
        cy.wait(1000);
        return attemptFind(attempt + 1);
      } else {
        throw new Error(`Element ${selector} not found after ${retries} retries`);
      }
    });
  };

  return attemptFind();
});

/**
 * Safe click with wait and retry
 * Usage: cy.safeClick('button')
 */
Cypress.Commands.add('safeClick', (selector, options = {}) => {
  const { timeout = 10000, force = false } = options;

  cy.get(selector, { timeout })
    .should('be.visible')
    .should('not.be.disabled')
    .click({ force });
});

/**
 * Enhanced admin session setup with better error handling
 * Usage: cy.setupAdminSession()
 */
Cypress.Commands.add("setupAdminSession", () => {
    const adminUrl = Cypress.env('adminUrl') || "https://staging.1800accountant.com/cbapi/app.php/accountant_login";
    return cy.fixture("stageAdmin.json").then((credentials) => {
        return cy.request({
            method: "POST",
            url: adminUrl,
            body: {
                username: credentials.username,
                password: credentials.password,
            }
        }).then((response) => {
            expect(response.status).to.eq(200);

            const sessionId = response.body.session_id;
            const staticUser = {
                id: "67a5b09b28fc6a50236a7b62",
                username: "<EMAIL>",
                sf_accountant_id: "005Ec00000GvKorIAF",
                active: false,
                cp_access: true,
                is_admin: true,
                first_name: "Swapnil",
                last_name: "Admin",
                profile: "Tax Accountant",
                sf_profile_id: "00ej0000000noBgAAI",
                sf_team_role: "Admin",
                password_expires_at: **********,
                team_size: 0,
                alias: "SAdmi",
                sf_username: "<EMAIL>",
                nickname: "sm_admin1",
                user_license: "Salesforce",
                email_encoding_key: "ISO-8859-1",
                zone: "Eastern",
                accounting_skills: [
                    "Tax Return - 1040", "Tax Return - 1120", "Tax Return - 1120S",
                    "Tax Return - 1065", "Tax Return - 990", "Tax Prep Review",
                    "Development Testing", "Easy VTO", "Hard VTO", "Medium VTO",
                    "TRQ Admin"
                ],
                can_handle_vip_clients: false,
                non_profit: false,
                international_taxation: false,
                languages: [],
                industries: [],
                timezone_sid_key: "America/New_York",
                locale_sid_key: "en_US",
                language_locale_key: "en_US",
                legal_zoom_legal_plan_id: "L-4669",
                bookkeeping_territory_multi: [],
                fulfillment_roles: [],
                can_prepare_returns: false,
                is_rc_authorized: false,
                lacerte_preparer_list_numbers: [{ year: "2023" }],
                last_login: **********,
                is_session_active: false,
                is_dedicated_accountant: false,
                permission: "ROLE_ADMIN",
                need_current_password_validate: true,
                team_role: "Accountant Team",
                is_manager_accountant: false,
                vonage_info: {},
                default_teams: [],
                access_level: "ADMIN"
            };

            const setCookies = response.headers['set-cookie'];
            const rememberMeHeader = Array.isArray(setCookies)
                ? setCookies.find(c => c.startsWith('REMEMBERME='))
                : setCookies;

            let rememberMeCookie = '';
            if (rememberMeHeader) {
                rememberMeCookie = rememberMeHeader.split(';')[0].split('=')[1];
            }

            // Set cookie
            cy.setCookie('REMEMBERME', rememberMeCookie, {
                domain: 'staging.1800accountant.com',
                secure: true,
                httpOnly: false,
                sameSite: 'Lax'
            });

            // Set localStorage on next page load
            Cypress.sessionData = {
                auth: JSON.stringify({ session_id: sessionId }),
                user: JSON.stringify(staticUser)
            };
        });
    });
});