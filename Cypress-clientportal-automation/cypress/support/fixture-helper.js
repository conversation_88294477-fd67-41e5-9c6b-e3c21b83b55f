/**
 * Fixture Helper Utilities
 * Provides centralized fixture management with environment-specific data
 */

/**
 * Load fixture with environment fallback
 * Usage: cy.loadFixture('credentials', 'uat')
 */
Cypress.Commands.add('loadFixture', (fixtureName, environment = 'uat') => {
  const envSpecificFixture = `${fixtureName}_${environment}`;
  
  return cy.task('fileExists', `cypress/fixtures/${envSpecificFixture}.json`)
    .then((exists) => {
      if (exists) {
        return cy.fixture(envSpecificFixture);
      } else {
        cy.log(`Environment-specific fixture ${envSpecificFixture} not found, using default ${fixtureName}`);
        return cy.fixture(fixtureName);
      }
    });
});

/**
 * Generate unique test data
 * Usage: cy.generateTestData('user')
 */
Cypress.Commands.add('generateTestData', (type) => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);
  
  const generators = {
    user: () => ({
      firstName: `Test${random}`,
      lastName: `User${timestamp}`,
      email: `test.user.${timestamp}@automation.com`,
      phone: `555${random.toString().padStart(4, '0')}`
    }),
    business: () => ({
      name: `Test Business ${timestamp}`,
      ein: `12-${random.toString().padStart(7, '0')}`,
      address: `${random} Test Street`,
      city: 'Test City',
      state: 'CA',
      zip: '90210'
    }),
    transaction: () => ({
      amount: (Math.random() * 1000).toFixed(2),
      description: `Test Transaction ${timestamp}`,
      category: 'Utilities',
      date: new Date().toISOString().split('T')[0]
    })
  };
  
  return generators[type] ? generators[type]() : {};
});

/**
 * Validate fixture data structure
 * Usage: cy.validateFixture(fixtureData, requiredFields)
 */
Cypress.Commands.add('validateFixture', (fixtureData, requiredFields = []) => {
  requiredFields.forEach(field => {
    expect(fixtureData).to.have.property(field);
    expect(fixtureData[field]).to.not.be.empty;
  });
  
  return cy.wrap(fixtureData);
});
