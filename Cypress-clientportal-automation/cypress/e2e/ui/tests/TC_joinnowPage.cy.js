import joinnowPage from"../../ui/pages/joinnowPage";
import AdminPortalLogin from "../../ui/pages/adminPortalLogin";

describe('Create brand new user using SSO flow', function () {

    before(function () {
        cy.fixture('joinnow').then(function(ssoData){
            this.ssoData=ssoData})
        cy.fixture('adminCredentials').then(function (adminCredentials) {
           this.adminCredentials = adminCredentials
        })
    })


it ('Validate new user creation', function(){
    const sso =new joinnowPage();
    sso.navigate(this.ssoData);
    const admin = new AdminPortalLogin();
    admin.adminLogin(this.adminCredentials)
    sso.contactSearchUsingEmailId(this.ssoData);
    sso.goToMyAccount(this.ssoData);

})


})

