import contactsPage from "../../ui/pages/contactsPage";

//Resuing the session from previous test case
beforeEach(() => {
    cy.restoreLocalStorage();
  });
describe('Add/Delete/Update Contacts', function() {


    it('Adding/Deleting/Updating Contacts' , function() {
        const contacts = new contactsPage();
       //Calling Contacts Methods
       //One Customer and One Vendor is created
       contacts.addNewContactCustomer();
       contacts.addNewContactVendor();
       //Newly Created Customer and Vendor are edited/updated with Phone Number
       contacts.searchEditContactCustomer();
       contacts.searchEditContactVendor();
       //Both Customer and Vendor are Deleted
       contacts.deleteContactCustomer();
       contacts.deleteContactVendor();
        

    })
    //Saving Session
    afterEach(() => {
        cy.saveLocalStorage();
      });
})
