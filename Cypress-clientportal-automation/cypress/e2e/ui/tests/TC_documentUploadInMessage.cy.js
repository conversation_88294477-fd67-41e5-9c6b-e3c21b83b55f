import MessagesPage from "../../ui/pages/messagesPage.js";

//Reusing the session from last test case
beforeEach(() => {
    cy.restoreLocalStorage();
  });

describe("Document upload in message", function() {

  before(function () {
    cy.fixture('messageDetails').then(function(msgdata) {
        this.msgdata = msgdata
    })
  })

    it("Document upload in message", function() {
        const msg= new MessagesPage();
        const filePath1= 'cypress/fixtures/images/Img1.png'; //providing the file path in a variable
        msg.docUploadInMsg(this.msgdata, filePath1);        
                
    })
    afterEach(() => {
        cy.saveLocalStorage();
      });

} )