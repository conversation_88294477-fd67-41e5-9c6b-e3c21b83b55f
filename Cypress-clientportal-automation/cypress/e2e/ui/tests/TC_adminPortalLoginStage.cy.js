describe('Admin portal Login via API', function () {

    it('Validate admin portal Login via API', function () {

        cy.setupAdminSession().then(() => {
            cy.visit('https://staging.1800accountant.com/admin/#/dashboard', {
                onBeforeLoad(win) {
                    win.localStorage.setItem('vto-admin.auth', Cypress.sessionData.auth);
                    win.localStorage.setItem('vto-admin.user', Cypress.sessionData.user);
                }
            });
            cy.url().should('include', '/dashboard');

        });

    })

})
