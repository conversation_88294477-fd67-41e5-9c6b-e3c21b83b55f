import AdminPortalLogin from "../../ui/pages/adminPortalLogin"

describe('Validate admin portal Login', function () {
   
    before(function () {
        cy.fixture('uatAdmin').then(function (adminCredentials) {
            this.adminCredentials = adminCredentials
        })
    })

    it('Validate admin portal Login', function () {
        const admin = new AdminPortalLogin();
        admin.adminLogin(this.adminCredentials);
    })

})
