import ClientCenter from "../../ui/pages/adminClientCenterPage"

//Resuing the session from previous test case
beforeEach(() => {
    cy.restoreLocalStorage();
  });

describe('Validate edit contact functionality', function () {

    it('Validate edit contact functionality', function () {

        const admin = new ClientCenter();
        admin.editContact();
    })

    //Saving Session
    afterEach(() => {
        cy.saveLocalStorage();
    });
})
