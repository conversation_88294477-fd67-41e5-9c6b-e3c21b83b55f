import JournalentryPage from"../../ui/pages/journalentrypage"

beforeEach(() => {
    cy.restoreLocalStorage();
  });

describe('Create, edit, delete Journal Entry on 1-800Accountant Portal', function () {


it ('Validate journal entry creation', function(){
    const journalentryPage=new JournalentryPage();
    journalentryPage.navigateje();
    journalentryPage.clickJournalentryButton();
    journalentryPage.add();
    journalentryPage.edit();
    journalentryPage.delete();
   
    

})
afterEach(() => {
    cy.saveLocalStorage();
  });

})