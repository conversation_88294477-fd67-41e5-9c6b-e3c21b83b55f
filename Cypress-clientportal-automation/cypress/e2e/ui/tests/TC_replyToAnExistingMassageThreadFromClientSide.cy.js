import MessagesPage from "../../ui/pages/messagesPage.js";

beforeEach(() => {
    cy.restoreLocalStorage();
  });

describe("Reply to an existing massage thread from client side", function() {

  before(function () {
    cy.fixture('messageDetails').then(function(msgdata) {
        this.msgdata = msgdata       
    })
  })

    it("Reply to an existing massage thread from client side", function() {
      const msg= new MessagesPage();
        msg.replyToExistingMessages(this.msgdata);
                
    })

  afterEach(() => {
        cy.saveLocalStorage();
      });

} )

