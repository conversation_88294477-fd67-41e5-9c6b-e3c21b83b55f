import mainNavigationControls from "../../ui/pages/mainNavigationControls"

describe('Main Navigation Sanity Check', function () {

  it('Main Navigation and Sanity Check', function () {
    const navigate = new mainNavigationControls();
    const title = '.MuiPaper-root .MuiToolbar-root .MuiTypography-root'

    navigate.selectNotificationsModule().each(($el) => {
      if ($el.text() === 'Notifications') {
        cy.wrap($el).click();
      }
    }
    );
    navigate.selectTeamModule().each(($el) => {
      if ($el.text() === 'Team') {
        cy.wrap($el).click();
      }
    }
    );
    navigate.selectDashboardModule().click();
    cy.get(title).should('have.text', 'Dashboard');
    navigate.selectDocumentsModule().click();
    cy.get(title).should('have.text', 'Documents');
    // navigate.selectCommunicationsModule().click();
    // cy.get(title).should('have.text', 'Communications');
    navigate.selectCalenderModule().click();
    cy.get(title).should('have.text', 'Calendar');

    //Bookkeeping Section
    navigate.selectBookkeepingSubSection().click();
    cy.wait(2000);
    navigate.selectTransactionsModule().click();
    cy.wait(2000);
    navigate.selectJournalEntriesModule().click();
    cy.get(title).should('have.text', 'Journal Entries');
    navigate.selectBankingModule().click();
    cy.wait(3000);
    cy.get(title).should('have.text', 'Banking');
    navigate.selectChartOfAccountsModule().click({ force: true });
    cy.get(title).should('have.text', 'Chart of Accounts');
    navigate.selectReconciliationsModule().click();
    cy.get(title).should('have.text', 'Reconciliations');
    navigate.selectReportsModule().click();
    cy.get(title).should('have.text', 'Reports');
    navigate.selectReceiptsScanModule().click();
    cy.get(title).should('have.text', 'Scan Receipts');

    //invoicing Section
    navigate.selectInvoicingSection().click();
    cy.wait(2000);
    navigate.selectInvoicesModule().click();
    cy.get(title).should('have.text', 'Invoices');
    navigate.selectContactsModule().click();
    cy.get(title).should('have.text', 'Contacts');
    navigate.selectProductsServicesModule().click();
    cy.get(title).should('have.text', 'Products/Services');
    navigate.selectManageCategoriesSection().click();
    cy.get(title).should('have.text', 'Manage Categories');

    //taxes Section
    navigate.selectTaxesSection().click();
    cy.wait(2000);
    navigate.selectBusinessTaxInformationModule().click();
    cy.get(title).should('have.text', 'Business Tax Information');
    navigate.selectPersonalTaxInformationModule().click();
    cy.get(title).should('have.text', 'Personal Tax Information');
    navigate.selectEstimatedTaxesModule().click();
    cy.get(title).should('have.text', 'Estimated Taxes');

    //Issue 1099 Section
    navigate.selectIssue1099Section().click();
    cy.wait(2000);
    navigate.selectFile1099NECModule().click();
    cy.get(title).should('have.text', 'File 1099-NECs');
    navigate.selectManage1099ContractorsModule().click();
    cy.get(title).should('have.text', 'Manage Contractor');

    //Mileage Log Section
    navigate.selectMileageLogSection().click();
    cy.wait(2000);
    navigate.selectTripsModule().click();
    cy.get(title).should('have.text', 'Trips');
    navigate.selectVehiclesModule().click();
    cy.get(title).should('have.text', 'Vehicles');
    navigate.selectLocationsModule().click();
    cy.get(title).should('have.text', 'Locations');
    navigate.selectPurposesModule().click();
    cy.get(title).should('have.text', 'Purposes');

  })
});