import MessagesPage from "../../ui/pages/messagesPage.js";

beforeEach(() => {
    cy.restoreLocalStorage();
  });

describe("View Archived Messages", function() {

  before(function () {
    cy.fixture('messageDetails').then(function(msgdata) {
        this.msgdata = msgdata
    })
  })

    it("View Archived Messages", function() {
        const msg= new MessagesPage();
        msg.viewArchiveMsg();
               
    })

    
    afterEach(() => {
        cy.saveLocalStorage();
      });

} )