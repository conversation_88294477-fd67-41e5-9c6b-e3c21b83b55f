import invoicesPage from "../../ui/pages/invoicesPage";

beforeEach(() => {
    cy.restoreLocalStorage();
  });

describe('Add/Edit/Update/Delete Invoices', function() {


    it('Adding/Editing/Deleting Invoices' , function() {
       const invoicesc = new invoicesPage();
       invoicesc.addInvoice();
       invoicesc.updateInvoice();
       invoicesc.deleteInvoice();
    })
    afterEach(() => {
        cy.saveLocalStorage();
      });
    })