import productsServicesPage from "../../ui/pages/productsServicesPage";

//Resuing the session from previous test case
beforeEach(() => {
    cy.restoreLocalStorage();
  });
describe('Add/Delete/Update Products & Services', function() {


    it('Adding/Deleting/Updating Products & Services' , function() {
        const productsServices = new productsServicesPage
       //Calling Products/Services Methods
       //One Product and One Service is created
       productsServices.addNewProduct();
       productsServices.addNewService();
       //Newly Created Product and Service are edited/updated
       productsServices.searchEditProduct();
       productsServices.searchEditService();
       //Both Product and Service are Deleted
       productsServices.deleteProduct();
       productsServices.deleteService();
       //Testing Category Filter
       productsServices.categoryTests();
               

    })
    //Saving Session
    afterEach(() => {
        cy.saveLocalStorage();
      });
})
