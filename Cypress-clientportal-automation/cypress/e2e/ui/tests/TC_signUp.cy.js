import SignupPage from"../../ui/pages/SignupPage"

describe('Validate Signup on 1-800Accountant Portal', function () {
    
before(function(){
    cy.fixture('signup').then(function(data){
        this.data=data
    })
  // Commenting the code below as this would be used once we have accessibility code at place 
  //  cy.injectAxe();
})


it ('Validate successful Signup', function(){
   // cy.checkA11y();
    const signupPage=new SignupPage();
    signupPage.navigate("https://uat.1800accountant.com/sso/#/sign_up")
    signupPage.email().type(this.data.email)
    signupPage.password().type(this.data.password)
    signupPage.repeatpassword().type(this.data.repeatpassword)
    signupPage.firstname().type(this.data.firstname)
    signupPage.lastname().type(this.data.lastname)
    signupPage.phone().type(this.data.phone)
    signupPage.checkbox()
    signupPage.clickSignup()
    signupPage.password().click()
    signupPage.password().type(this.data.password)
    signupPage.clickSignin()
    signupPage.businessNameInput()
    signupPage.businessType()
    signupPage.businessIndustry()
    signupPage.businessformation()
    signupPage.proceedToPortalBtn()
    
})
//Saving Session to resuse in next test case
afterEach(() => {
    cy.saveLocalStorage();
  });
})