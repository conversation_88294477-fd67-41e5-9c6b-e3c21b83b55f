import ManageContractorPage from "../../ui/pages/manageContractorPage";
import file1099NEC from "../../ui/pages/file1099NECPage";


describe('Add new contractor for 1099 tax form on 1-800Accountant Portal', function () {

  before(function () {
    cy.fixture('1099ContractorData').then(function (ContractorData) {
      this.ContractorData = ContractorData
    })

  })

  it('Add contractors and 1099 Filing', function () {
    const contractor = new ManageContractorPage();

    //Add new contractor
    contractor.addNewContractor(this.ContractorData);

    //Edit first name and last name of a contractor
    contractor.editContractor()

    //Import Contractors
    const filePath = 'cypress/fixtures/images/W9_DATA_IMPORT_TEMPLATE1.csv'; //providing the file path in a variable
    contractor.importContractors(filePath)

    //pdate EIN and user address for 100 filing
    contractor.PayerEinAndAddressUpdate();

    //1099 filing for single and multiple contractors
    const file1099 = new file1099NEC();
    file1099.file1099ForSingleContractor()
    file1099.file1099ForAllContractors()

  }) 
   
})
