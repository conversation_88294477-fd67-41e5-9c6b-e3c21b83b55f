import adpPayroll from "../../ui/pages/adpPayrollSetupPage";
//¸import Employee from "../ui/pages/payrollEmployeeCreation";
import 'cypress-iframe';
import adpEmployee from "../../ui/pages/adpPayrollEmployeeCreation";



//Reusing the session from last test case
beforeEach(() => {
  cy.restoreLocalStorage();

});

describe('ADP Payroll Setup', function () {

  before(function () {
    cy.fixture('ADPPayrollData').then(function (ADPPayrollData) {
      this.ADPPayrollData = ADPPayrollData
    })
    cy.fixture('creditCardDetailsADP').then(function (creditCardDetailsADP) {
      this.creditCardDetailsADP = creditCardDetailsADP
    })
  })

  it(' ADP Payroll setup', function () {

    const compadp = new adpPayroll();   
    compadp.businessSetup(this.ADPPayrollData,this.creditCardDetailsADP);
    const emp = new adpEmployee();
    emp.adpNewEmployee(this.ADPPayrollData);

  })

  afterEach(() => {
    cy.saveLocalStorage();
  });

})




