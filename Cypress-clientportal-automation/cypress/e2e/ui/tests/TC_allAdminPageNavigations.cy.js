import alladminPages from "../../ui/pages/allAdminPageNavigation"

//Resuing the session from previous test case
beforeEach(() => {
    cy.restoreLocalStorage();
});

describe('All Admin Page Navigations', function () {

    it('All Admin Page Navigations', function () {

        const admin = new alladminPages();
        const PageTitle = '.breadcrumb'

        // admin.allAdminPagenavigation();
        admin.selectDashboard().click({ force: true });
        cy.get(PageTitle).should('contain', 'Dashboard');
        admin.selectClientCenter().click({ force: true });
        cy.get(PageTitle).should('contain', 'Contacts');
        admin.selectCalendar().click({ force: true });
        cy.get(PageTitle).should('contain', 'Calendar');
        admin.selectMessageCenter().click({ force: true });
        cy.get(PageTitle).should('contain', 'Conversations');
        admin.selectDocuments().click({ force: true });
        cy.get(PageTitle).should('contain', 'Documents');
        admin.selectTaxAdvisory().click({ force: true });
        cy.get(PageTitle).should('contain', 'Tax Advisory');
        admin.selectTaxCenter().click({ force: true });
        cy.get(PageTitle).should('contain', 'Tax Prep');
        admin.selectBookkeeping().click({ force: true });
        cy.get(PageTitle).should('contain', 'Bookkeeping');
        admin.selectMorePageOptions().click({ force: true });
        admin.selectPayroll().click({ force: true });
        cy.get(PageTitle).should('contain', 'Payroll');
        admin.selectEntityFormation().click({ force: true });
        cy.get(PageTitle).should('contain', 'Entity Formation');
        admin.selectVendasta().click({ force: true });
        cy.get(PageTitle).should('contain', 'Vendasta');
        admin.selectCases().click({ force: true });
        cy.get(PageTitle).should('contain', 'Cases');
        admin.selectReminders().click({ force: true });
        cy.get(PageTitle).should('contain', 'Reminders');
        admin.selectUsers().click({ force: true });
        cy.get(PageTitle).should('contain', 'Users');
        admin.selectManagerView().click({ force: true });
        cy.get(PageTitle).should('contain', 'Manager View');
        admin.selectManagerViewNew().click({ force: true });
        cy.get(PageTitle).should('contain', 'Manager View New');
        admin.selectReports().click({ force: true });
        cy.get(PageTitle).should('contain', 'NY Tax Manager Dashboard');

    })
    //Saving Session
    afterEach(() => {
        cy.saveLocalStorage();
    });

})
