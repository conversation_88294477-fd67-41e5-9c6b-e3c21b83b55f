import ManualbankPage from "../../ui/pages/ManualbankPage";

beforeEach(() => {
    cy.restoreLocalStorage();
  });

describe('Add Manual Bank Account', function() {

    before(function () {
        cy.fixture('manualBankDetails').then(function (bankdata) {
            this.bankdata = bankdata
        })
    })

    it('Adding Manual Bank Account' , function() {
       const mBankaccount = new ManualbankPage();
       mBankaccount.manualBank();
       mBankaccount.bankNameInput().type(this.bankdata.AccountName)
       mBankaccount.bankType();
       mBankaccount.amount().type(this.bankdata.StartBalance)
       mBankaccount.nextBtn();
       mBankaccount.bankDetails();
       mBankaccount.nextBtn();
       mBankaccount.createAccount();
       mBankaccount.editAccount();
    })
    afterEach(() => {
        cy.saveLocalStorage();
      });
    })

    