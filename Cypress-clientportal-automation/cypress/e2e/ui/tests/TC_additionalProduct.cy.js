import additionalProductsPage from "../../ui/pages/additionalProductsPage";

//Reusing the session from last test case
beforeEach(() => {
    cy.restoreLocalStorage();
  });

      describe('Right Navigation and Additional product purchase', function() {


        it('Right Navigation and Additional product purchase' , function() {
            const addProduct = new additionalProductsPage();
            addProduct.navigate();
        })
      })

      afterEach(() => {
        cy.saveLocalStorage();
      });