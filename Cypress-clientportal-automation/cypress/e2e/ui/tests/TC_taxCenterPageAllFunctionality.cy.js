import taxCenterAdminPage from "../../ui/pages/taxCenterAdminPage";

describe('Perform All Tax Center Functionalities', function() {

    it('Perform all the operations on Tax Center Page', function() {
     
        const taxCenter = new taxCenterAdminPage();

        //Calling taxCenterAdminPage class methods
        taxCenter.taxCenterPageDropdown();
        taxCenter.searchAddTaxPrepAP();
        taxCenter.searchAddSalesUseTaxAP();
        taxCenter.searchAddNoticeAP();
        taxCenter.searchAddStateTaxFilingAP();
        taxCenter.searchAdd1099PrepAP();
        taxCenter.searchAddExtensionAP();
        taxCenter.searchAddEstimatedTaxesAP();
        taxCenter.editAP();
        

    }
    )
    
}
)