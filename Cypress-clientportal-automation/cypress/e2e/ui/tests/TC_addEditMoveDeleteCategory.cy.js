import chartOfAccounts from "../../ui/pages/chartOfAccounts";

//Resuing the session from previous test case

describe('Add/Edit/Move/Delete COA', function() {


    it('Adding/Editing/Moving/Deleting COA' , function() {
        const coa = new chartOfAccounts();
       //Calling Chart of Accounts Methods
       //One Level 4 account getting created
       coa.addNewCategory();
       //Created Level 4 account edited
       coa.editCategory();
      //Created Level 4 account moved to the Level 3 account
       coa.moveCategory();
      //Level 4 account deleted
       coa.deleteCategory();
       //coa.runReportCategory();

    })
   
})
