import ClientCenter from "../../ui/pages/adminClientCenterPage"

//Resuing the session from previous test case
beforeEach(() => {
    cy.restoreLocalStorage();
});

describe('Validate Contact and Account search Case', function () {

    it('Validate Contact and Account search Case', function () {

        const admin = new ClientCenter();
        admin.contactSearchUsingEmailId();
    })

    //Saving Session
    afterEach(() => {
        cy.saveLocalStorage();
    });
})

