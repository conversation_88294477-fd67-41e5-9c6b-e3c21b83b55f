import documentsPage from "../../ui/pages/documentsPage";

//Resuing the session from previous test case
beforeEach(() => {
    cy.restoreLocalStorage();
  });
describe('Upload Documents in Business and Private Path', function() {


    it('Multiple Actions for Documents in Business and Private Path' , function() {
        const documents = new documentsPage();
       //Calling Documents Page Methods
        // documents.businessDocumentUpload();
        // documents.deleteDocument();
        documents.privateDocumentUpload();
        documents.downloadDocument();
        documents.renameDocument();
        documents.moveDocument();
        documents.filtersDocument();

    })
    //Saving Session
    afterEach(() => {
        cy.saveLocalStorage();
      });
})