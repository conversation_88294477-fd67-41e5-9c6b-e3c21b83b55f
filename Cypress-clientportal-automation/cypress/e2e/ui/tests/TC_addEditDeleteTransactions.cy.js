import transactionsPage from "../../ui/pages/transactionsPage";

//Resuing the session from previous test case
beforeEach(() => {
    cy.restoreLocalStorage();
  });
describe('Add/Delete/Update Transactions', function() {


    it('Adding/Deleting/Updating Transactions' , function() {
        const transactions = new transactionsPage();
       //Calling Transactions Methods
        transactions.addTransaction();
        transactions.filtersTransaction();
        transactions.editTransaction();
        transactions.deleteTransaction();
        
        

    })
    //Saving Session
    afterEach(() => {
        cy.saveLocalStorage();
      });
})
