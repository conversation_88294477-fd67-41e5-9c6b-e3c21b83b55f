import MessagesPage from "../../ui/pages/messagesPage.js";

beforeEach(() => {
    cy.restoreLocalStorage();
  });

describe("Start new converstation", function() {

  before(function () {
    cy.fixture('messageDetails').then(function(msgdata) {
        this.msgdata = msgdata
    })
  })

    it("Start new converstation", function() {
        const msg= new MessagesPage();
        msg.startNewConversation();
        msg.addSubject(this.msgdata);
        msg.selectRecipient().should('have.text', this.msgdata.recipient).click();        
        msg.writeTheMessage(this.msgdata);
        msg.sendMsg();
        
    })
    afterEach(() => {
        cy.saveLocalStorage();
      });

} )