import CompanyDetails from "../../ui/pages/companyDetailsPayrollSetup"
import Employee from "../../ui/pages/payrollEmployeeCreation";

//Reusing the session from last test case
beforeEach(() => {
  cy.restoreLocalStorage();

});

describe('Payroll Setup', function () {

  before(function () {
    cy.fixture('PayrollData').then(function (payrollData) {
      this.payrollData = payrollData
    })
    cy.fixture('creditCardDetails').then(function (creditCardDetails) {
      this.creditCardDetails = creditCardDetails
    })
  })

  it('Payroll setup', function () {

    const business = new CompanyDetails();   
    business.businessSetup(this.payrollData, this.creditCardDetails);
    const emp = new Employee();
    emp.newEmployee(this.payrollData);

  })

  afterEach(() => {
    cy.saveLocalStorage();
  });

})




