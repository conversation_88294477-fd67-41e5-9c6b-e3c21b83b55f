import loginPage from '../../ui/pages/loginPage.js';

describe('Validate Login on 1-800Accountant Portal', function () {

    before(function () {
        cy.fixture('credentials').then(function (credentials) {
            this.credentials = credentials
        })
    })

    it('Validate successful Login', function () {
        const login = new loginPage();

        cy.visit("https://uat.1800accountant.com/sso/#/login")
        cy.wait(2000);
        cy.log('Credentials: ', this.credentials.username, this.credentials.password, this.credentials.otp);
        login.signInWithEmail()
        cy.wait(2000);
        login.usernameInput().type(this.credentials.username)
        login.passwordInput().type(this.credentials.password)
        login.googleRecaptchaCheck()
        login.loginBtnClick()
        login.getOtpOnEmail()
        cy.wait(2000);
        login.getOtp().type(this.credentials.otp)
        login.validateOtp()
        cy.wait(5000);
        cy.get('body').click(); // Click elsewhere to avoid the pop up or mousehover
        cy.get('.MuiPaper-root .MuiToolbar-root .MuiTypography-root').as('Title').should('have.text', 'Dashboard')

    });
});


