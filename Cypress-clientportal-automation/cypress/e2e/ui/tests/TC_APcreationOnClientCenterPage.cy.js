import ClientCenter from "../../ui/pages/adminClientCenterPage"

//Resuing the session from previous test case
beforeEach(() => {
    cy.restoreLocalStorage();
});

describe('Validate AP creation on client center page', function () {

    before(function () {
        cy.fixture('testUser').then(function (testUser) {
            this.testUser = testUser
        })

    })

    it('Validate AP creation on client center page', function () {

        const admin = new ClientCenter();
        admin.payrollAPCreation(this.testUser);
    })

    //Saving Session
    afterEach(() => {
        cy.saveLocalStorage();
    });
})

