import UserPage from "../../ui/pages/UserPage";

describe('Accountant Creation', function () {

    before(function () {
        cy.fixture('accountantData').then(function (accountantData) {
            this.data = accountantData
        })
    })


    it('Accountant Creation', function () {
        const user = new UserPage();
        this.data.forEach((userdata) => {
            cy.get('a[href="#/dashboard"]').click({ force: true })
            cy.wait(2000)
            cy.get('a[href="#/accountants"]').click({ force: true })
            cy.wait(2000)
            user.AddUser(userdata);
        });


    });


})
