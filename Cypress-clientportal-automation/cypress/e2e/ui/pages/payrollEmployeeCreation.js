class Employee {

    emp_first_name = 'input[name="first_name"]'
    emp_middle_initial = 'input[name="middle_initial"]'
    emp_last_name = 'input[name="last_name"]'
    emp_email = 'input[name="email"]'
    emp_date_of_birth = 'input[name="date_of_birth"]'
    emp_ssn = 'input[name="ssn"]'
    next = '.MuiButton-label'

    emp_hire_date = 'input[name="hire_date"]'
    emp_title = 'input[name="title"]'
    emp_locationDD = '#mui-component-select-location_id'
    emp_location = '#Texas'

    emp_PayAmount = 'input[name="rate"]'
    emp_pay_frequencyDD = '#mui-component-select-payment_unit'
    emp_pay_frequency = '#Month'
    emp_perc_ownership_yes = '.MuiFormGroup-root > :nth-child(1)'
    emp_perc_ownership_no = '.MuiFormGroup-root > :nth-child(2)'


    newEmployee(payrollData) {
        //cy.contains('Dashboard').click()
        //cy.contains('Payroll').click();
        cy.contains('Employees').click();
        cy.contains('New Employee').click()
        cy.get(this.emp_first_name).type(payrollData.emp_first_name)
        cy.get(this.emp_middle_initial).type(payrollData.emp_middle_initial)
        cy.get(this.emp_last_name).type(payrollData.emp_last_name)
        cy.get(this.emp_email).type(payrollData.emp_email)
        cy.get(this.emp_date_of_birth).type(payrollData.emp_date_of_birth)
        cy.get(this.emp_ssn).type(this.uniqueSSN())
        cy.get(this.next).contains('Next').click()
        this.addJob(payrollData);
    }

    addJob(payrollData) {
        cy.get(this.emp_hire_date).type(payrollData.emp_hire_date)
        cy.get(this.emp_title).type(payrollData.emp_title)
        cy.get(this.emp_locationDD).click()
        cy.get(`#${payrollData.emp_location}`).click()
        cy.get(this.next).contains('Next').click()
        this.addCompensation(payrollData)
    }

    addCompensation(payrollData) {
        cy.get(this.emp_PayAmount).clear().type(payrollData.emp_PayAmount)
        cy.get(this.emp_pay_frequencyDD).click()
        cy.get(`#${payrollData.emp_pay_frequency}`).click()
        cy.get(this.next).contains('Next').click()
        this.ownershipPercentage(payrollData)
    }

    ownershipPercentage(payrollData) {
        if(payrollData.emp_perc_ownership == "yes"){
            cy.get(this.emp_perc_ownership_yes).click();
        }else{
            cy.get(this.emp_perc_ownership_no).click();
        }        
        cy.get(this.next).contains('Save Employee').click()
    }

    uniqueSSN() {
        var uniqueSSN = "";
        var possible = "012345678";
        for (var i = 0; i < 9; i++)
            uniqueSSN += possible.charAt(Math.floor(Math.random() * possible.length));
        return uniqueSSN;
    }

}
export default Employee