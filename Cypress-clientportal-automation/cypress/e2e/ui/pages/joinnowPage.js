class joinnowPage {
    
    timeslot='#time-slot-10 > div'
    firstname = '#first_name'
    lastname = '#last_name'
    email = '#email'
    phone='#phone'
    industry='#industry'
    businessname='#business_name'

    clientCenter = 'a[href="#/users"]'
    emailSearch = 'input-search > .col-xs-3 > .form-control'
    searchButton = 'span[class="items-search-btn"]'
    contactLink = 'a[class="ng-star-inserted"]'
    adminLoginbtn='.btn.btn-default.ng-star-inserted[data-cy="1800-accountant-login-button"]'

    entitytype='#business_type'
    businessindustry='#industry'
    state='#form_state'
    closebtn='button[aria-label="close"]'

    navigate(ssoData) {
        cy.visit('https://uat.1800accountant.com/join-now');
        cy.wait(2000);
        cy.get(this.timeslot).click();
        cy.get(this.firstname).type(ssoData.firstname)
        cy.get(this.lastname).type(ssoData.lastname)
        cy.get(this.email).type(ssoData.email)
        cy.get(this.phone).type(ssoData.phone)
        cy.get(this.businessname).type(ssoData.businessname)
        cy.get(this.industry).select(ssoData.industry)
        cy.wait(5000);
        cy.contains('Confirm My Appointment').click({ force: true });
        cy.wait(20000); 
    }
            
    contactSearchUsingEmailId(ssoData) {
                cy.get(this.clientCenter).eq(0).click({ force: true });
                cy.wait(3000);
                cy.get(this.emailSearch).type(ssoData.email);
                cy.wait(3000);
                cy.get(this.searchButton).click({force:true});
                cy.wait(3000);
                cy.get(this.contactLink).click({ multiple: true });
                cy.wait(4000)
                cy.get(this.adminLoginbtn).invoke('removeAttr', 'target').click({ multiple: true });
                cy.wait(3000);
            }
        
    
    goToMyAccount(ssoData){
       
    
        cy.get(this.entitytype).click();
        cy.get('.MuiAutocomplete-option').contains(ssoData.entitytype).click();
        cy.wait(3000);
        cy.get(this.state).click()
        cy.get('.MuiAutocomplete-option').contains(ssoData.state).click();
        cy.wait(3000);
        cy.contains('Go to my Account').click();
        cy.wait(3000)
        cy.get(this.closebtn).click();
          
         }
    
}
   
  

  export default joinnowPage