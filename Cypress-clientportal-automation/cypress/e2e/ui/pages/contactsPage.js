class contactsPage{

    //This method is adding New Contact with Customer Type and with Random Name
    addNewContactCustomer()
    {
        cy.visit('/')
        cy.wait(10000)
        cy.contains('Bookkeeping').click();
        cy.wait(3000)
        cy.contains('Contacts').click();
        cy.wait(3000)
        cy.contains('New Contact').click();
        const contactname = () => Cypress._.random(0, 1e6)
        const id = contactname()
        const conctCName = `Automation Customer ${id}`
        cy.get('input[name="name"]').type(conctCName);
        cy.get('input[name="email"]').type('<EMAIL>');
        cy.wait(3000)
        cy.contains('Save Changes').click();
        cy.get('.MuiAlert-message').should('have.text', 'Contact created successfully');
        cy.wait(3000)
    }

    //This method is adding New Contact with Vendor Type and with Random Name
    addNewContactVendor()
    {
        cy.wait(3000)
        cy.contains('New Contact').click();
        cy.wait(3000)
        const contactname = () => Cypress._.random(0, 1e6)
        const id = contactname()
        const conctVName = `Automation Vendor ${id}`
        cy.get('input[name="name"]').type(conctVName);
        cy.get('[value="vendor"]').click();
        cy.wait(3000)
        cy.get('input[name="email"]').type('<EMAIL>');
        cy.contains('Save Changes').click();
        cy.wait(3000)
        cy.get('.MuiAlert-message').should('have.text', 'Contact created successfully');
        cy.wait(3000)
    }

    //This method uses search functionality to search newly created Contact with Customer type, Edit the contact and enteres the phone number
    searchEditContactCustomer()
    {
        cy.get('input[placeholder="Search"]').click().type('Automation Customer').type('{enter}');
        cy.wait(3000)
        cy.get('.infinite-scroll-component > :nth-child(1)').click();
        cy.wait(3000)
        cy.contains('Edit').click();
        cy.wait(3000)
        cy.get('input[placeholder="Mobile"]').type('8983300240');
        cy.wait(3000)
        cy.contains('Save Changes').click();
        cy.wait(3000)
    }

    //This method uses search functionality to search newly created Contact with Vendor type, Edit the contact and enteres the phone number
    searchEditContactVendor()
    {
        cy.wait(3000)
        cy.get('input[placeholder="Search"]').clear();
        cy.get('input[placeholder="Search"]').click().type('Automation Vendor').type('{enter}');
        cy.get('.infinite-scroll-component > :nth-child(1)').click();
        cy.contains('Edit').click();
        cy.wait(3000)
        cy.get('input[placeholder="Mobile"]').type('8983300240');
        cy.contains('Save Changes').click();
        cy.wait(3000)
    }

    //This method finds existing created Contact with Customer Type and Deletes the contact
    deleteContactCustomer()
    {
        cy.wait(3000)
        cy.get('input[placeholder="Search"]').clear();
        cy.get('input[placeholder="Search"]').click().type('Automation Customer').type('{enter}');
        cy.get('.infinite-scroll-component > :nth-child(1)').click();
        cy.contains('Delete').click();
        cy.wait(2000)
        cy.get('button[id="delete-confirm-button"]').click();
        cy.get('.MuiAlert-message').should('have.text', 'Contact deleted successfully');
        cy.wait(3000)
    }  


    //This method finds existing created Contact with Customer Type and Deletes the contact
    deleteContactVendor()
    {
        cy.wait(3000)
        cy.get('input[placeholder="Search"]').clear();
        cy.get('input[placeholder="Search"]').click().type('Automation Vendor').type('{enter}');
        cy.wait(3000)
        cy.get('.infinite-scroll-component > :nth-child(1)').click();
        cy.contains('Delete').click();
        cy.wait(2000)
        cy.get('button[id="delete-confirm-button"]').click();
        cy.get('.MuiAlert-message').should('have.text', 'Contact deleted successfully');
        cy.get('input[placeholder="Search"]').clear();
        cy.wait(3000)
    }

}
export default contactsPage