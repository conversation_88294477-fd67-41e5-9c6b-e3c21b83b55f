class addBusinessPage{


    addNewBusiness(){
        cy.contains('Dashboard').click()
        cy.get('div[data-cy="change-business-btn"]').click()
        cy.get('a[href="#/business/new_account"]').click()
        cy.get('body').click(); // Click elsewhere to avoid the pop up or mousehover
        cy.get('input[name="name"]').type('2nd Business')
        cy.get('input[name="ein"]').type('*********')
        cy.get('div[id="mui-component-select-business_type"]').click()
        cy.get('li[id="S"]').click() // S Corporation
        cy.get('div[id="mui-component-select-industry"]').click()
        cy.get('li[id="BA"]').click() //banking
        cy.get('input[name="form_date"]').type('01/01/2020')
        cy.get('div[id="mui-component-select-form_state"]').click()
        cy.get('li[id="NY"]').click()
        cy.get('input[name="phone_type_us"]').check('No'); //Radio button selection
        cy.get('input[name="phone_type_us"]').check('Yes');

        //Business address
        cy.get('input[name="address.address"]').type('Test1')
        cy.get('input[name="address.city"]').type('New york')
        cy.get('div[id="mui-component-select-address.state"]').click()
        cy.get('li[id="NY"]').click()
        cy.get('input[name="address.zip_code"]').type('10001')

        //owner address
        cy.get('input[name="business_owners[0].address.address"]').type('Test2')
        cy.get('input[name="business_owners[0].address.city"]').type('Dallas')
        cy.get('input[name="business_owners[0].address.county"]').type('US')
        cy.get('div[id="mui-component-select-business_owners[0].address.state"]').click()
        cy.get('li[id="TX"]').click()
        cy.get('input[name="business_owners[0].address.zip_code"]').type('75001')

        cy.get('button[type="button"]').contains("Proceed to Portal").click({force: true})
        cy.wait(5000)

    }

}

export default addBusinessPage