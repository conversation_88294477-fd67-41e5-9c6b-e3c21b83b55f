class loginPage {

    usernameInput() {
        return cy.get('input[name="username"]')

    }

    passwordInput() {
        return cy.get('input[name="password"]')

    }

    googleRecaptchaCheck() {
        cy.wait(3000)
        cy.get('iframe[title="reCAPTCHA"]').then($iframe => {
            const $body = $iframe.contents().find('body')
            cy.wrap($body)
              .find('#recaptcha-anchor')
              .should("be.visible")
            .click();
            cy.wait(3000)
          })
    }

    signInWithEmail() {
        cy.get('.MuiTypography-root').each(($el) => {
            if ($el.text() === 'Sign in with your email') {
                cy.wrap($el).click({force: true });
            }
        })
    }

    getOtpOnEmail() {
        cy.get('.MuiFormControlLabel-root:nth-child(2)').click()   
        cy.get('button[type="submit"]').click()
    }

    getOtp() {
    return cy.get('input[name="otp"]')
    }


    validateOtp() {
        cy.get('button[type="submit"]').click();
    }

    loginBtnClick() {
        cy.get('button[type="button"]').contains('Sign In').click()
        cy.wait(6000)

    }
}
export default loginPage