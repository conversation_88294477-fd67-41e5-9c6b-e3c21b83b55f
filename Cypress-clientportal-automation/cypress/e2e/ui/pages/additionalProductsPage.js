class additionalProductsPage{

    personalDetails='[data-cy="open-user-popover-btn"]' //right top button to view the personal detail/signout option
    signOut = '[data-cy="sign-out-btn"]'
    //additionalProducts = '[data-cy="goto-products-btn"]'
    //labelPayroll = '.MuiContainer-fixed > .MuiList-root > :nth-child(1)'
    labelPayroll= '.MuiList-root > :nth-child(25)'
    checkout = '.MuiContainer-root > .MuiButtonBase-root'

    creditCardNumber ='input[id="creditcard_crypt_number"]'
    creditCardExpiryMonth ='input[id="creditcard_expdate_month"]'
    creditCardExpiryYear = 'input[id="creditcard_expdate_year"]'
    creditCardCVV = 'input[id="creditcard_cvv"]'
    creditCardStreetAddr = 'input[id="creditcard_crypt_address"]'
    creditCardCity = 'input[id="creditcard_crypt_city"]'
    creditCardState = 'input[id="creditcard_crypt_state"]'
    creditCardPostalcode ='input[id="creditcard_crypt_postcode"]'
    creditCardCountryDD ='div[id="creditcard_crypt_country_chosen"]'  // dropdown (DD)
    creditCardSubmitButton = 'button#btn_creditcard_submit'
    


    purchasePayrollProduct(purchase) {
    cy.wait(3000);
    cy.get(this.personalDetails).click(); //right top button to view the personal detail/signout option
    cy.wait(5000);
    cy.contains('Additional Products').click();
    cy.wait(5000);
    cy.contains('Additional Products').should('have.text', 'Additional Products');
    cy.wait(3000);
    cy.get(this.labelPayroll).contains('Payroll').click();
    cy.contains('button','Checkout').click();
    this.purchaseProduct(purchase);
}

  //   purchaseProduct(purchase) {
  //       cy.wait(20000);
  //         // Declare the iframe selector
  //      // const iframeSelector = '#charge_over_iframe';
  //      const iframe = '#charge_over_iframe';
  //      //const iframe2='.drift-frame-controller';
  //      //const textToType = '****************';
  //       //Ensure the iframe is loaded
  //       cy.frameLoaded(iframe);
  //    // Switch context to the iframe and use cy.within() to scope subsequent commands
  //     cy.iframe(iframe).within(() => {
  //     // Find and click the element inside the iframe
  //     cy.contains('Complete order now').then($element => {
  //       if ($element.length) {
  //         // Element is present and clicked
  //         cy.wrap($element).click();
  //         cy.log('Element with the text "Complete order now" is present and clicked.');
  //         cy.wait(10000);       
  //         //cy.iframe().find("input#creditcard_crypt_number").type(textToType,{force: true});
  //  } 
  //       else {
  //         // Element is not present
  //         cy.log('Element with the text "Complete order now" is not present.');

  //             }
  //           });
  //         });
  //         cy.frameLoaded(iframe);
  //         cy.iframe(iframe).within(() => {
  //           cy.get(this.creditCardNumber,{ timeout: 30000 }).should('be.visible').type(purchase.creditCardNumber, { force: true });
  //           cy.get(this.creditCardExpiryMonth,{ timeout: 30000 }).should('be.visible').type(purchase.creditCardExpiryMonth, { force: true });
  //           cy.get(this.creditCardExpiryYear,{ timeout: 30000 }).should('be.visible').type(purchase.creditCardExpiryYear, { force: true });
  //           cy.get(this.creditCardCVV,{ timeout: 30000 }).should('be.visible').type(purchase.creditCardCVV, { force: true });
  //           cy.get(this.creditCardStreetAddr,{ timeout: 30000 }).should('be.visible').type(purchase.creditCardStreetAddr, { force: true });
  //           cy.get(this.creditCardCity,{ timeout: 30000 }).should('be.visible').type(purchase.creditCardCity, { force: true });
  //           cy.get(this.creditCardState,{ timeout: 30000 }).should('be.visible').type(purchase.creditCardState, { force: true });
  //           cy.get(this.creditCardPostalcode,{ timeout: 30000 }).should('be.visible').type(purchase.creditCardPostalcode, { force: true });
  //           cy.get(this.creditCardCountryDD).click();
  //           cy.get(this.creditCardCountryDD).type('United States{enter}');
  //           cy.get(this.creditCardSubmitButton).click();
  //           cy.wait(5000);
  //            });

  //           };

  //       }
       
  

  purchaseProduct(purchase) {
    cy.wait(15000);

    // Locate and interact with the first iframe (Click on 'a.btn-success')
    cy.get('iframe[src*="chargeover.com/r/salesforce"]').should('exist').then($iframe => {
        cy.wrap($iframe).its('0.contentDocument.body')
            .should('not.be.empty') 
            .then(cy.wrap)
            .find('a.btn-success') 
            .eq(1)
            .click();
    });

    cy.wait(5000);  

    //Locate and interact with the same iframe again to fill out credit card details
    cy.get('iframe[src*="chargeover.com/r/salesforce"]').should('exist').then($iframe => {
        cy.wrap($iframe).its('0.contentDocument.body')
            .should('not.be.empty')
            .then(cy.wrap)
            .within(() => {  
                cy.get(this.creditCardNumber, { timeout: 30000 }).should('be.visible').type('****************');
                cy.get(this.creditCardExpiryMonth, { timeout: 30000 }).should('be.visible').type('10');
                cy.get(this.creditCardExpiryYear, { timeout: 30000 }).should('be.visible').type('26');
                cy.get(this.creditCardCVV, { timeout: 30000 }).should('be.visible').type('389');
                cy.get(this.creditCardStreetAddr, { timeout: 30000 }).should('be.visible').type('Street No 15');
                cy.get(this.creditCardCity, { timeout: 30000 }).should('be.visible').type('NY');
                cy.get(this.creditCardState, { timeout: 30000 }).should('be.visible').type('New York');
                cy.get(this.creditCardPostalcode, { timeout: 30000 }).should('be.visible').type('00501');
                cy.get(this.creditCardCountryDD).click();
                cy.get(this.creditCardCountryDD).type('United States{enter}');
                cy.get(this.creditCardSubmitButton).click();
            });
    });

    cy.wait(5000);
}

}

export default additionalProductsPage

