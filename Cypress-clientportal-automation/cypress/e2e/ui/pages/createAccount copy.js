class CreateAccount {

  firstName = 'input[name="firstName"]'
  lastName = 'input[name="lastName"]'
  email = 'input[name="email"]'
  password = 'input[name="password"]'
  confirmPassword = 'input[name="confirmPassword"]'
  phone = 'input[name="phoneN<PERSON>ber"]'
  businessName = 'input[name="businessName"]'
  createAccount = 'button[type="button"]'

  //Additional Business Details
  businessTypeDD = '#business_type'
  industryDD = '#industry'
  stateDD = '#form_state'
  dropdownListValue = '.MuiAutocomplete-option'
  GoToMyAccount = '.MuiButton-label'




  userAccountCreation(userNumber) {
    //url
    cy.visit('https://uat.1800accountant.com/sso/#/create-account')
    //User details
   
    //cy.get(this.firstName).type(`DemoFname` + this.randomNum().slice(0,3))
    //cy.get(this.lastName).type(`Lname` + this.randomNum().slice(0,3))
    //cy.get(this.email).type(`automationuser` + this.randomNum().slice(0,4) + `@uat.com`) 
    
    cy.get(this.firstName).type(`Swapnil` + userNumber);
    cy.get(this.lastName).type(`Mule` + userNumber)
    cy.get(this.email).type(`sm${userNumber}_uat` + `@test.com`) 
    cy.get(this.password).type('111111')
    cy.get(this.confirmPassword).type('111111')
    cy.get(this.phone).type(this.randomNum())
    cy.get(this.businessName).type(`sm${userNumber} B1`)
    cy.get(this.createAccount).click()
    cy.wait(10000)

    //Next Page for business details 
    cy.get(this.businessTypeDD).click();
    cy.get(this.dropdownListValue).contains("S Corporation").click();
    cy.get(this.industryDD).click();
    cy.get(this.dropdownListValue).contains("Banking").click();
    cy.get(this.stateDD).click();
    cy.get(this.dropdownListValue).contains("Alabama").click();
    cy.get(this.GoToMyAccount).contains('Go to my Account').click()
    cy.wait(3000)

  }

  randomNum() {
    var randomNum = "";
    var possible = "**********";
    for (var i = 0; i < 10; i++)
      randomNum += possible.charAt(Math.floor(Math.random() * possible.length));
    return randomNum;
  }

}

export default CreateAccount