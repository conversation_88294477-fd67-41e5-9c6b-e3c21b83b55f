class taxCenterAdminPage
{
    //This method is for Tax Center page Dropdown
    taxCenterPageDropdown()
    {
        cy.get('a[href="#/tax-prep"]').click({ force: true })      
        cy.wait(2000)
        cy.get('#select_ap_type').select('1: app.sales-list')
        cy.wait(2000)
        cy.get('#select_ap_type').select('2: app.notice-list')
        cy.wait(2000)
        cy.get('#select_ap_type').select('3: app.state_tax_filing-list')
        cy.wait(2000)
        cy.get('#select_ap_type').select('4: app.prep1099-list')
        cy.wait(2000)
        cy.get('#select_ap_type').select('5: app.extension-list')
        cy.wait(2000)
        cy.get('#select_ap_type').select('6: app.estimated_taxes-list')
        cy.wait(2000)
        cy.get('a[href="#/tax-prep"]').click({ force: true })
        cy.wait(3000)
        
    }

    //Automate Add and Search 'Tax Prep AP'
    searchAddTaxPrepAP()
    {
        cy.get('a[href="#/tax-prep"]').click({ force: true })      
        cy.wait(2000)

        //Add Tax Prep AP
        cy.contains('Add AP').click()
        cy.wait(2000)
        cy.contains('Ok').click()
        cy.get('[data-cy="accounting-ap-type"]').click().type('Sample Tax Prep AP1'); 
        cy.wait(2000)
        cy.get('[data-cy="account"]').click().type('Jay')
        cy.wait(2000)
        cy.contains('Jay Bhor 181').click()
        cy.wait(2000)
        cy.get('[data-cy="tax-accountant"]').click().type('1')
        cy.wait(2000)
        cy.contains('1-800Accountant Team').click()
        cy.wait(2000)
        cy.get('[data-cy="admin-support"]').click().type('1')
        cy.wait(2000)
        cy.contains('1-800Accountant Team').click()
        cy.wait(2000)
        cy.get('[data-cy="reviewer"]').click().type('1')
        cy.wait(2000)
        cy.contains('Test Acc111').click()
        cy.wait(4000)
        cy.get('#select_task_type').select('1: tax_1040_individual');
        cy.wait(2000)
        cy.contains('Save').click()
        cy.wait(3000)
        cy.contains('Tax Center').click({ force: true });
        cy.wait(3000)
        cy.get('#select_ap_type').select('0: app.tax_prep-list')
        cy.wait(2000)
        

        //Search Tax Prep AP
        cy.get('[data-cy="clear-filters"]').click()
        cy.wait(2000)
        cy.get('[data-cy="search"]').click().type('Sample Tax Prep AP1');
        cy.wait(3000)
        cy.get('[data-cy="search-icon"]').click()
        cy.wait(3000)
        cy.contains('Sample Tax Prep AP1').click()
        cy.wait(2000)
        cy.contains('Tax Center').click({ force: true });
        cy.wait(2000)

    }

    //Automate Add and Search 'Sales and Use Tax AP'
    searchAddSalesUseTaxAP()
    {
        cy.get('a[href="#/tax-prep"]').click({ force: true })
        cy.wait(2000)
        cy.get('#select_ap_type').select('1: app.sales-list')
        cy.wait(2000)
        
        //Add Sales and Use Tax AP
        cy.contains('Add AP').click()
        cy.wait(2000)
        cy.contains('Ok').click()
        cy.get('[data-cy="process_name"]').click().type('Sample Sales and Use Tax AP1'); 
        cy.wait(2000)
        cy.get('[data-cy="account"]').click().type('Jay')
        cy.wait(2000)
        cy.contains('Jay Bhor 181').click()
        cy.wait(2000)
        cy.contains('Save').click()
        cy.wait(3000)
        cy.contains('Tax Center').click({ force: true });
        cy.wait(3000)
        cy.get('#select_ap_type').select('1: app.sales-list')
        cy.wait(2000)

        //Search Sales and Use Tax AP
        //cy.get('[data-cy="clear-filters"]').click()
        cy.wait(2000)
        cy.get('[data-cy="search"]').click().type('Sample Sales and Use Tax AP1');
        cy.wait(3000)
        cy.get('[data-cy="search-icon"]').click()
        cy.wait(3000)
        cy.contains('Sample Sales and Use Tax AP1').click()
        cy.wait(2000)
        cy.contains('Tax Center').click({ force: true });
        cy.wait(2000)

    }


    //Automate Add and Search 'Notice AP'
    searchAddNoticeAP()
    {
        cy.get('a[href="#/tax-prep"]').click({ force: true })
        cy.wait(3000)
        cy.get('#select_ap_type').select('2: app.notice-list')
        cy.wait(2000)

        //Add Notice AP
        cy.contains('Add AP').click()
        cy.wait(2000)
        cy.contains('Ok').click()
        cy.get('[data-cy="process_name"]').click().type('Notice Sample AP1'); 
        cy.wait(2000)
        cy.get('[data-cy="account"]').click().type('Jay')
        cy.wait(2000)
        cy.contains('Jay Bhor 181').click()
        cy.wait(2000)
        cy.contains('Save').click()
        cy.wait(3000)
        cy.contains('Tax Center').click({ force: true });
        cy.wait(3000)
        cy.get('#select_ap_type').select('2: app.notice-list')
        cy.wait(2000)
        
        //Search Notice AP
        //cy.get('[data-cy="clear-filters"]').click()
        cy.wait(2000)
        cy.get('[data-cy="search"]').click().type('Notice Sample AP1');
        cy.wait(3000)
        cy.get('[data-cy="search-icon"]').click()
        cy.wait(3000)
        cy.contains('Notice Sample AP1').click()
        cy.wait(2000)
        cy.contains('Tax Center').click({ force: true });
        cy.wait(2000)

    }

    //Automate Add and Search 'Stae Tax Filing AP'
    searchAddStateTaxFilingAP()
    {
        cy.get('a[href="#/tax-prep"]').click({ force: true })
        cy.wait(3000)
        cy.get('#select_ap_type').select('3: app.state_tax_filing-list')
        cy.wait(2000)
        
        //Add State Tax Filing AP
        cy.contains('Add AP').click()
        cy.wait(2000)
        cy.contains('Ok').click()
        cy.get('[data-cy="process_name"]').click().type('State Tax Filing Sample AP1'); 
        cy.wait(2000)
        cy.get('[data-cy="account"]').click().type('Jay')
        cy.wait(2000)
        cy.contains('Jay Bhor 181').click()
        cy.wait(2000)
        cy.contains('Save').click()
        cy.wait(2000)
        cy.get('a[href="#/tax-prep"]').click({ force: true })
        cy.wait(3000)
        cy.get('#select_ap_type').select('3: app.state_tax_filing-list')
        cy.wait(2000)

        //Search State Tax Filing AP
        //cy.get('[data-cy="clear-filters"]').click()
        cy.wait(2000)
        cy.get('[data-cy="search"]').click().type('State Tax Filing Sample AP1');
        cy.wait(3000)
        cy.get('[data-cy="search-icon"]').click()
        cy.wait(3000)
        cy.contains('State Tax Filing Sample AP1').click()
        cy.wait(2000)
        cy.contains('Tax Center').click({ force: true });
        cy.wait(2000)

    }


    //Automate Add and Search '1099 Prep AP'
    searchAdd1099PrepAP()
    {
        cy.get('a[href="#/tax-prep"]').click({ force: true })
        cy.wait(3000)
        cy.get('#select_ap_type').select('4: app.prep1099-list')
        cy.wait(2000)
        
        //Add 1099 Prep AP
        cy.contains('Add AP').click()
        cy.wait(2000)
        cy.contains('Ok').click()
        cy.get('[data-cy="process_name"]').click().type('1099 Prep Sample AP1'); 
        cy.wait(2000)
        cy.get('[data-cy="account"]').click().type('Jay')
        cy.wait(2000)
        cy.contains('Jay Bhor 181').click()
        cy.wait(2000)
        cy.contains('Save').click()
        cy.wait(2000)
        cy.get('a[href="#/tax-prep"]').click({ force: true })
        cy.wait(3000)
        cy.get('#select_ap_type').select('4: app.prep1099-list')
        cy.wait(2000)

        //Search 1099 Prep AP
        //cy.get('[data-cy="clear-filters"]').click()
        cy.wait(2000)
        cy.get('[data-cy="search"]').click().type('1099 Prep Sample AP1');
        cy.wait(3000)
        cy.get('[data-cy="search-icon"]').click()
        cy.wait(3000)
        cy.contains('1099 Prep Sample AP1').click()
        cy.wait(2000)
        cy.contains('Tax Center').click({ force: true });
        cy.wait(2000)
    }


    //Automate Add and Search 'Extension AP'
    searchAddExtensionAP()
    {
        cy.get('a[href="#/tax-prep"]').click({ force: true })
        cy.wait(3000)
        cy.get('#select_ap_type').select('5: app.extension-list')
        cy.wait(2000)

        //Add Extension AP
        cy.contains('Add AP').click()
        cy.wait(2000)
        cy.contains('Ok').click()
        cy.wait(2000)
        cy.get('[data-cy="process_name"]').click().type('Extension Sample AP1'); 
        cy.wait(2000)
        cy.get('[data-cy="account"]').click().type('Jay')
        cy.wait(2000)
        cy.contains('Jay Bhor 181').click()
        cy.wait(2000)
        cy.get('#select_year').select('3: 2022')
        cy.wait(2000)
        cy.contains('Save').click()
        cy.wait(3000)
        cy.contains('Tax Center').click({ force: true });
        cy.wait(3000)
        cy.get('#select_ap_type').select('5: app.extension-list')
        cy.wait(2000)
        
        //Search Extension AP
        cy.get('[data-cy="clear-filters"]').click()
        cy.wait(2000)
        cy.get('[data-cy="search"]').click().type('Extension Sample AP1');
        cy.wait(3000)
        cy.get('[data-cy="search-icon"]').click()
        cy.wait(3000)
        cy.contains('Extension Sample AP1').click()
        cy.wait(2000)
        cy.contains('Tax Center').click({ force: true });
        cy.wait(2000)

    }


    //Automate Add and Search 'Estimated Taxes AP'
    searchAddEstimatedTaxesAP()
    {
        cy.get('a[href="#/tax-prep"]').click({ force: true })
        cy.wait(3000)
        cy.get('#select_ap_type').select('6: app.estimated_taxes-list')
        cy.wait(2000)
        
        //Add Estimated Taxes AP
        cy.contains('Add AP').click()
        cy.wait(2000)
        cy.contains('Ok').click()
        cy.get('[data-cy="process_name"]').click().type('Estimated Taxes Sample AP1'); 
        cy.wait(2000)
        cy.get('[data-cy="account"]').click().type('Jay')
        cy.wait(2000)
        cy.contains('Jay Bhor 181').click()
        cy.wait(2000)
        cy.get('#select_tax_year').select('3: 2022')
        cy.wait(2000)
        cy.get('#select_estimated_taxes_status').select('2: additional_info_requested')
        cy.wait(2000)
        cy.get('#select_estimated_taxes_uarter').select('1: q2')
        cy.wait(2000)
        cy.contains('Save').click()
        cy.wait(2000)
        cy.get('a[href="#/tax-prep"]').click({ force: true })
        cy.wait(3000)
        cy.get('#select_ap_type').select('6: app.estimated_taxes-list')
        cy.wait(2000)

        //Search Estimated Taxes AP
        cy.get('[data-cy="clear-filters"]').click()
        cy.wait(2000)
        cy.get('[data-cy="search"]').click().type('Estimated Taxes Sample AP1');
        cy.wait(3000)
        cy.get('[data-cy="search-icon"]').click()
        cy.wait(3000)
        cy.contains('Estimated Taxes Sample AP1').click()
        cy.wait(2000)
        cy.contains('Tax Center').click({ force: true });
        cy.wait(2000)

    }


    //Automate Edit added AP
    editAP()
    {
        cy.contains('Tax Center').click({ force: true });
        cy.wait(3000)
        cy.get('#select_ap_type').select('0: app.tax_prep-list')
        cy.wait(2000)
        cy.get('[data-cy="clear-filters"]').click()
        cy.wait(2000)
        cy.get('[data-cy="search"]').click().type('Sample Tax Prep AP1');
        cy.wait(3000)
        cy.get('[data-cy="search-icon"]').click()
        cy.wait(3000)
        cy.contains('Sample Tax Prep AP1').click()
        cy.wait(2000)
        cy.contains('Edit').click()
        cy.wait(2000)
        cy.get('#select_task_type').select('4: tax_1120_Corporation');
        cy.wait(2000)
        cy.contains('Save').click()
        cy.wait(3000)
        cy.contains('Tax Center').click({ force: true });
        cy.wait(3000)
    }



}
export default taxCenterAdminPage