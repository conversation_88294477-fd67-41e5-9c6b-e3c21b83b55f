
class ReportsPage {

    //This method will generate the Balnce sheet reports
    generateBalanceSheet()
    {
        //cy.visit('/')
        cy.contains('Bookkeeping').click();
        cy.wait(5000)
        cy.get('[data-cy="expandable-section-child-Reports"]').click();
        cy.wait(5000)
        cy.get('[href="#/reports?name=balanceSheet"]').click();
        cy.wait(5000)
        //This will generate Balance Sheet as of 'Today'
        cy.get('[data-cy="today-chip-btn"]').click();          //Can be skipped, by default on this option
        cy.get('[data-cy="generate-report-btn"]').click();     //We don't have any transactions so script is failing
        cy.wait(5000)
        cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(5000)
        //This will generate Balance sheet of 'Lat Year'
        cy.get('[data-cy="last-year-chip-btn"]').click();
        cy.get('[data-cy="generate-report-btn"]').click();
        cy.wait(5000)
        cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(5000)
        
    }

    //This method will generate Income Statement for selected durations
    generateIncomeStatement()
    {
        cy.get('[href="#/reports?name=incomeStatement"]').click();
        //This will generate Income Statement for 'This Year'
        cy.get('[data-cy="this-year-chip-btn"]').click();         //Can be skipped, by default on this option
        cy.get('[data-cy="generate-report-btn"]').click();
        cy.wait(5000)
        cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(5000)
        //This will generate Income Statement for 'Last Quarter'
        cy.get('[data-cy="last-quater-chip-btn"]').click();
        cy.get('[data-cy="generate-report-btn"]').click();
        cy.wait(5000)
        cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(5000)
        //This will generate Income Statement for 'Last Year'
        cy.get('[data-cy="last-year-chip-btn"]').click();
        cy.get('[data-cy="generate-report-btn"]').click();
        cy.wait(5000)
        cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(5000)
        //Will add Comparison reports later
    }

    //This method will generate Invoices Report for selected period of Time
    generateInvoicesReport()
    {
        cy.get('[href="#/reports?name=invoices"]').click();
        cy.get('[data-cy="issue-date-btn"]').click();               //Can skip this as by default selected option is for 'Issue Date'
        //This will generate Invoices report for 'This Year'
        cy.get('[data-cy="this-year-chip-btn"]').click();           //Can be skipped, by default on this option
        cy.get('[data-cy="generate-report-btn"]').click();
        cy.wait(5000)
        cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(5000)
        //This will generate Invoices report for 'This Month'---->We can skip this if no invoices available for This Month
        //cy.get('[data-cy="this-month-chip-btn"]').click();
        //cy.get('[data-cy="generate-report-btn"]').click();
        //cy.wait(3000)
        //cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(5000)
        //This will generate the invoices report for 'Three Months'
        cy.get('[data-cy="three-months-chip-btn"]').click();
        cy.get('[data-cy="generate-report-btn"]').click();
        cy.wait(5000)
        cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(5000)
        //This will generate the invoices report for 'All Time''
        cy.get('[data-cy="all-time-chip-btn"]').click();
        cy.get('[data-cy="generate-report-btn"]').click();
        cy.wait(5000)
        cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(5000)
        //This will generate the invoices report for Due Date 'This Year'
        cy.get('[data-cy="due-date-btn"]').click();
        cy.get('[data-cy="this-year-chip-btn"]').click(); 
        cy.get('[data-cy="generate-report-btn"]').click();
        cy.wait(5000)
        cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(5000)


    }

    //This method will generate the Mileage Log reports for selected period of time
    generateMileageLogReport()
    {
        cy.get('[href="#/reports?name=mileageLog"]').click();
        //This will generatethe Mileage log for Business trip activity of 'This Year'
        cy.get('[data-cy="search-vehicle-input"]').click();
        cy.wait(1000)
        cy.get('[data-cy="cross-modal-close-btn"]').click();
        cy.wait(1000)
        cy.get('[data-cy="this-year-chip-btn"]').click(); 
        cy.get('[data-cy="generate-report-btn"]').click();
        cy.wait(5000)
        cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(3000)
    }

    //This will generate the Reconciliations report  of 'This Year'
    generateReconciliationsReport()
    {
      cy.get('[href="#/reports?name=reconciliations"]').click();
      cy.get('[data-cy="select-account-input"]').click();
      cy.wait(1000)
      cy.get('[data-cy="cross-modal-close-btn"]').click();
      cy.wait(1000)
      cy.get('[data-cy="this-year-chip-btn"]').click(); 
      cy.get('[data-cy="generate-report-btn"]').click();
      cy.wait(2000)
      cy.get('[data-cy="generate-new-report-btn"]').click();
      cy.wait(3000)

    }

    //This will generate Transactions by Account report for 'This Year'
    generateTransactionsByAccountReport()
  {
        cy.get('[href="#/reports?name=transactions"]').click();
        cy.get('[data-cy="account-input"]').click();
        cy.contains('ARTESTREPORTEdited').click();
        cy.wait(2000)
        cy.get('[data-cy="this-year-chip-btn"]').click(); 
        cy.wait(2000)
        cy.get('[data-cy="generate-report-btn"]').click();
        cy.wait(3000)
        cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(3000)
    }
    
    //This will generate Journal Entries report for 'This Year'
    generateJournalEntriesReport()
    {
        cy.get('[href="#/reports?name=journalEntries"]').click();
        cy.get('[data-cy="this-year-chip-btn"]').click(); 
        cy.get('[data-cy="generate-report-btn"]').click();
        cy.wait(5000)
        cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(5000)
    }
    
    //This will generate General Journal report for 'This Year'
    generateGeneralJournalReport()
    {
        cy.get('[href="#/reports?name=generalJournal"]').click();
        cy.get('[data-cy="this-year-chip-btn"]').click(); 
        cy.get('[data-cy="generate-report-btn"]').click();
        cy.wait(5000)
        //This is generic to test 'Save Report' multiple options
        cy.get('[data-cy="save-report-btn"]').click();
        cy.get('[data-cy="download-as-pdf_btn"]').click();
        cy.wait(6000)
        //cy.get('[data-cy="save-to-portal-as-pdf_btn"]').click();  //Need ID after this tab to handle pop-up
        cy.get('[data-cy="save-report-btn"]').click();
        cy.get('[data-cy="download-as-csv_btn"]').click();
        cy.wait(6000)
        //cy.get('[data-cy="save-to-portal-as-csv_btn"]').click();  //Need ID after this tab to handle pop-up
        cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(5000)

    }

    //This will generate General Ledger report for 'This Year'
    generateGeneralLedgerReport()
    {
        cy.get('[href="#/reports?name=generalLedger"]').click();
        cy.get('[data-cy="this-year-chip-btn"]').click(); 
        cy.get('[data-cy="generate-report-btn"]').click();
        cy.wait(5000)
        cy.get('[data-cy="generate-new-report-btn"]').click();
        cy.wait(5000)
        
    }




}
export default ReportsPage