import additionalProductsPage from "./additionalProductsPage";

class personalVto {
    checkbox = 'input[type="checkbox"]'
    ein = 'input[id="business.ein"]'
    businessaddress = '[data-cy="business.address.address"]'
    city = '[data-cy="business.address.city"]'
    stateofissuance = '[data-cy="personalInfo.general.driver_license_state"]'
    stateofissuancedrop = 'li[data-option-index="0"]'
    zipcode = '[data-cy="business.address.zip_code"]'
    occupation = '[data-cy="personalInfo.general.occupation"]'
    licensenumber = '[data-cy="personalInfo.general.driver_license_number"]'
    taxfiling = '[data-cy="personalInfo.general.filing_status"]'
    taxfilingdrop = 'li[data-value="single"]'
    homeaddress = '[data-cy="personalInfo.general.address.address"]'
    homecity = '[data-cy="personalInfo.general.address.city"]'
    homestate = '[data-cy="personalInfo.general.address.state"]'
    //Timezone as per state index value PST-California(4), EST-New York (32), CST-Texas (44)
    homestatedrop = 'li[data-option-index="0"]'
    homezipcode = '[data-cy="personalInfo.general.address.zip_code"]'
    homecounty = '[data-cy="personalInfo.general.address.county"]'
    dependents = 'input[value="false"][name="personalInfo.dependants_has_dependant"]'
    education = 'input[value="false"][name="personalInfo.contributed_education"]'
    income = 'input[name="income.has_no_income"]'
    contractorincome = 'input[name="income.has_contract_income"]'
    deduction = 'input[name="deductions.has_no_deductions"]'
    k1form = '[data-cy="forms_count.k1"]'
    foreignassets = 'input[value="false"][name="additionalQuestions.has_foreign_assets"]'
    taxpayments = 'input[value="false"][name="additionalQuestions.has_estimated_tax_payments"]'
    directdeposit = 'input[value="false"][name="additionalQuestions.has_direct_deposit"]'
    directdebit = 'input[value="false"][name="additionalQuestions.has_direct_debit"]'
    irs = 'input[value="0"][name="forms_count.irs_corespondence"]'
    nextyearservice = 'input[name="assist_next_year.not_needed"]'
    requireddoc = 'input[name="select-all"]'
    ssn = 'input[id="personalInfo.general.ssn"]'
    dob = '[data-cy="personalInfo.general.birthday"]'
    expirationdate = '[data-cy="personalInfo.general.driver_license_expiration"]'
    issuedate = '[data-cy="personalInfo.general.driver_license_issue"]'
    employee = 'input[value="false"][name="business.has_employees"]'
    balancesheet = '[data-cy="business.software_for_bookkeeping_other"]'
    balancesheetcount = 'input[value="1"][name="forms_count.balance_sheet"]'
    balancesheetprofit = 'input[value="1"][name="forms_count.balance_profit"]'
    estimatedpayments = 'input[value="false"][name="business.misc_any_estimated_payments"]'
    foreigninvolve = 'input[value="false"][name="business.misc_foreign_involve"]'
    comments = 'textarea[name="submitDetails.additional_info"]'
    agreecheckbox = 'input[name="submitDetails.submit_confirm"]'
    fullname = 'input[name="submitDetails.consent_disclosed_by"]'


    //This method is for PTP page
    personalTaxPrepAp() {
        cy.wait(5000)
        cy.viewport(1920, 1080);
        cy.contains('Taxes').click();
        cy.wait(2000)
        cy.contains('Personal Tax Information').click();
        cy.wait(2000)
        cy.get(this.checkbox).click();
        cy.contains('Get Started').click();
        cy.wait(3000)
        cy.get('span', { log: false, timeout: 0 }).then(($spans) => {
            const nextSpan = $spans.toArray().find((el) => el.innerText.trim() === "Next");
            if (nextSpan) {
                // If Business VTO is already submitted
                cy.wrap(nextSpan).click();
                cy.contains('p', 'I was not required to file').should('be.visible').click();
                cy.contains('span', 'I don’t have a secondary form of payment').should('be.visible').click();
                cy.contains('span', 'Okay, Got It').should('be.visible').click();
                // cy.get(this.checkbox).click(); //this page is dynamic and available from 1st jan- 15th april
                // cy.contains('p', 'Continue To My Draft Tax Return').click();
                cy.contains('p', '1040 Form Scan').should('be.visible').click();
                cy.contains('p', 'I was not required to file').should('be.visible').click();
                this.personalVTOForm(); // Filling the Personal VTO form
                cy.wait(5000);
                cy.contains('Button', 'dashboard').click();
                cy.reload();
            } else {
                // If Business VTO is not submitted yet
                cy.contains('span', 'I don’t have Business Taxes for 2024').click();
                cy.contains('span', 'Next', { timeout: 10000 }).should('be.visible').click();
                cy.contains('p', 'I was not required to file').should('be.visible').click();
                //cy.get(this.checkbox).click(); //this page is dynamic and available from 1st jan- 15th april
                //cy.contains('p', 'Continue To My Draft Tax Return').click();
                cy.contains('p', '1040 Form Scan').should('be.visible').click();
                cy.contains('p', 'I was not required to file').should('be.visible').click();
                this.personalVTOForm(); // Filling the Personal VTO form
                const pp = new additionalProductsPage();
                pp.purchaseProduct();
                cy.contains('Button', 'dashboard').click();
                cy.reload();
            }

        });
    }

    personalVTOForm() {
        cy.get(this.ssn).eq(0).type('*********');
        cy.get(this.dob).first().type('11/11/2024');
        cy.get(this.occupation).first().type('dummy test data');
        cy.get(this.licensenumber).first().type('**********');
        cy.get(this.stateofissuance).eq(1).click();
        cy.get(this.stateofissuancedrop).click({ force: true });
        cy.get(this.expirationdate).first().type('10/10/2027');
        cy.get(this.issuedate).first().type('03/28/2023');
        cy.get(this.checkbox).eq(3).click();
        cy.get(this.ssn).eq(0).type('*********');
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.taxfiling).eq(0).click();
        cy.get(this.taxfilingdrop).click({ force: true });
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.homeaddress).eq(0).type('East street #34 avenue building');
        cy.get(this.homecity).eq(0).type('New York');
        cy.get(this.homestate).eq(1).click();
        cy.get(this.homestatedrop).click({ force: true });
        cy.get(this.homezipcode).eq(0).type('778988');
        cy.get(this.homecounty).eq(0).type('US');
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.dependents).click({ force: true });
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.education).click({ force: true });
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.contractorincome).then(($checkbox) => {
            if ($checkbox.attr('value') === 'true') {
                cy.wrap($checkbox).uncheck({ force: true }); 
            }
        });
        cy.get(this.income).should('exist').check({ force: true });
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.deduction).should('exist').check({ force: true });
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.k1form).eq(0).type('1');
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.foreignassets).click({ force: true });
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.taxpayments).click({ force: true });
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.directdeposit).click({ force: true });
        cy.get(this.directdebit).click({ force: true });
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.irs).click({ force: true });
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.nextyearservice).should('exist').check({ force: true });
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.requireddoc).eq(0).click();
        cy.get(this.requireddoc).eq(1).click();
        cy.contains('Button', 'Submit Your Tax Info').click();
        cy.get(this.comments).first().type('Dummy additional info');
        cy.get(this.agreecheckbox).click({ force: true });
        cy.get('#consent-form').scrollTo('bottom').should('be.visible');
        cy.get(this.fullname).first().type('Demo Test User');
        cy.contains('Button', 'Next').click();
        cy.contains('Button', 'No Thanks').click();
        cy.contains('Button', 'Submit Your Tax Info').click({ force: true });
    }

}

export default personalVto