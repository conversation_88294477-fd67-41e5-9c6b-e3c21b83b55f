class file1099NEC {

    File1099NECsLink = 'a[href= "#/contractors/file-1099-nec"]'
    taxYearDDLB = 'div[id="tax_yar_outlined"]'
    contractorSelectCheckbox = 'input[data-cy="select"]'
    allcontractorSelectCheckbox = 'input[data-cy="select-all"]'
    amount = 'input[name="currency"]'
    search = 'input[placeholder="Search"]'
    file1099 = 'button[data-cy="file1099-btn"]'
    contractorCountOnCurrentPage = '.MuiTablePagination-root'

    file1099ForSingleContractor() {
        cy.contains('Dashboard').click();
        cy.wait(6000)
        cy.contains('Issue 1099').click();
        cy.wait(2000)
        cy.get(this.File1099NECsLink).click()
        cy.get(this.taxYearDDLB).click()
        cy.get('[data-value="2023"]').click() //filing 1099 for tax year 2023

        cy.get(this.contractorSelectCheckbox).eq(0).click()
        cy.get(this.amount).eq(0).clear().type('100')
        cy.get(this.search).click()
        //the search click is for to go away from the amount field to make update API call
        cy.wait(2000)
        cy.get(this.file1099).click({ force: true })

    }

    file1099ForAllContractors() {
        cy.wait(2000)
        cy.get(this.File1099NECsLink).click()
        cy.get(this.taxYearDDLB).click()
        cy.get('[data-value="2022"]').click() //filing 1099 for tax year 2022

        cy.get(this.allcontractorSelectCheckbox).eq(0).click()
        let notfiledCount = null
        cy.get(this.contractorCountOnCurrentPage)
            .then(($el) => {
                notfiledCount = $el.text().slice(2, 4)
                cy.log(notfiledCount) //count of all not filed contractors from the current page
                //adding the amount paid for all not filed contractors
                for (var i = 0; i < notfiledCount; i++) {
                    cy.get(this.amount).eq(i).clear().type(`${i + 1}00`)
                    cy.get(this.search).click()
                    cy.wait(2000)
                }

            })
        //Filing 1099 for all selecyed contractors together
        cy.get(this.file1099).click({ force: true })
    }

}

export default file1099NEC
