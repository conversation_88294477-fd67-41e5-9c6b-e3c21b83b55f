class transactionsPage {

   addTransaction()
   {
      //Random Function for Transaction Amount [This will create Debit/Credit Transaction Randomly]
      const transactionAmount = () => Cypress._.random(-100, 1e3)
      const tAmount = transactionAmount()
      //Navigating to Transactions
      cy.visit('/')
   cy.wait(2000)
    cy.contains('Transactions').click({force:true});
    cy.reload()
    cy.wait(5000)
    cy.get('[class*=MuiButton-containedPrimary]').click();
    cy.wait(2000)
    //Adding Transacton Details random amount, Description with Utilities Category
    cy.get('[class*=MuiGrid-grid-md-6]').eq(3).click();
    cy.get('[class*=MuiGrid-grid-md-6]').eq(3).type('{backspace}');
    cy.get('[class*=MuiGrid-grid-md-6]').eq(3).type(tAmount);
    cy.get(':nth-child(6) > .MuiGrid-root > .MuiFormControl-root > .MuiInputBase-root').eq(1).type('This is Automated Transaction');
    cy.get('[class*=MuiAutocomplete-root]').eq(1).type('Utilities');
    cy.contains('Expenses').click();
    cy.get('[class*=MuiButton-contained]').eq(2).click();
    cy.wait(2000)
   
   } 
   deleteTransaction()
   {
      //Searching Newly Created Transaction from Search Option
      cy.wait(3000)
      cy.get('.MuiGrid-grid-xs-11 > .MuiInputBase-root').type('This is Automated Transaction').type('{enter}');
      cy.contains('Utilities').click();
      cy.contains('Delete').click();
      //Deleting the Transaction with Confirmation and Asserting if transaction is successfully deleted
      cy.get('[class*=MuiButton-contained]').eq(1).click();
      cy.get('.MuiSnackbar-root > .MuiPaper-root').contains('deleted');
   }

   editTransaction()
   {
      cy.wait(2000)
      cy.contains('Utilities').click();
      cy.contains('Edit').click();
      //Clearing the description box and typing new text which confirms the transaction is edited
      cy.get(':nth-child(6) > .MuiGrid-root > .MuiFormControl-root > .MuiInputBase-root').clear();
      cy.get(':nth-child(6) > .MuiGrid-root > .MuiFormControl-root > .MuiInputBase-root').type('This is Edited Transaction');
      //Saving the changes
      cy.contains('Save Changes').click();
      cy.wait(3000)
   }

   filtersTransaction()
   {
      cy.contains('Filters').click();
        cy.wait(2000)
        cy.contains('All Time').click();
        cy.wait(2000)
        cy.contains('This Week').click();
        cy.wait(2000)
        cy.contains('Apply Filters').click();
        cy.wait(2000)
        cy.get('.MuiBox-root > .MuiChip-root > .MuiChip-label').should('contain', 'This Week');
   }
}
export default transactionsPage