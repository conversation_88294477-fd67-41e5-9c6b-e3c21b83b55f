class ManageContractorPage {

  //Add new contractor
  manageContractors = 'a[href= "#/contractors/manage"]'
  button = 'span[class="MuiButton-label"]'
  firstName = 'input[name="first_name"]'
  lastName = 'input[name="last_name"]'
  email = 'input[name="email"]'
  phone = 'input[name="phone"]'
  radioOption = 'div[class="MuiFormGroup-root"]'
  bizName = 'input[name="business_name"]'
  taxYearDDLB = 'div[id="mui-component-select-contractor_w_nine[0].year"]'
  TIN = 'input[name="tax_identification_number"]'
  street = 'input[name="street"]'
  city = 'input[name="city"]'
  state = 'input[id="state"]'
  stateDD = '#state-popup'
  zip = 'input[name="zip"]'
  saveContractor = 'button[data-cy="save-new-cat-btn"]'

  //Edit contractor
  contractorCount = '#contractor-count'
  selectContractor = 'div[id="contractor-list"]'

  //business Detail and My Detail page
  extraOptionBtn = ".MuiSvgIcon-root"
  bizDetail = 'li[data-cy="view-business-detail"]'
  ein = 'input[name="ein"]'
  myDetails = '.MuiListItemText-root'


  addNewContractor(Contractor) {
    cy.contains('Dashboard').click();
    cy.wait(6000)
    cy.contains('Issue 1099').click();
    cy.wait(3000)
    cy.get(this.manageContractors).click();
    cy.get(this.button).contains("Add a Contractor").click();
    cy.get(this.firstName).type(Contractor.firstName)
    cy.get(this.lastName).type(Contractor.lastName)
    cy.get(this.email).type(`1099Contractor` + this.randomNum().slice(0, 4) + `@uat.com`)
    cy.get(this.phone).type(this.randomNum())
    let contractorType = Contractor.contractor_Type
    cy.get(this.radioOption).contains(contractorType).click();
    cy.get(this.bizName).type(Contractor.bizName)
    cy.get(this.taxYearDDLB).click();
    cy.get(`#${Contractor.taxyear}`).click()
    let TinType = Contractor.TIN_Type
    cy.get(this.radioOption).contains(TinType).click();
    cy.get(this.TIN).type(*********)
    cy.get(this.street).type(Contractor.street)
    cy.get(this.city).type(Contractor.city)
    cy.get(this.state).type(Contractor.state).click();
    cy.get(this.stateDD).contains(Contractor.state).click();
    cy.get(this.zip).type(Contractor.zip)
    cy.wait(2000)
    cy.get(this.saveContractor).click({ force: true })
    cy.wait(6000)

  }

  randomNum() {
    var randomNum = "";
    var possible = "1234567890";
    for (var i = 0; i < 10; i++)
      randomNum += possible.charAt(Math.floor(Math.random() * possible.length));
    return randomNum;
  }

  editContractor() {
    cy.wait(2000)
    cy.get(this.manageContractors).click();
    //To check if we have any contractor created before
    cy.get(this.contractorCount).then(($body) => {
      if ($body.text().includes('0 Contractors')) {
        cy.log("No contractor were Found")

      } else {
        cy.get(this.selectContractor).eq(0).click() //select 1st contractor from the list
        cy.wait(4000)
        cy.get(this.button).contains('Edit').click()
        cy.get(this.firstName).clear().type("Pravin") //updating first and last name
        cy.get(this.lastName).clear().type("Kudale")
        cy.get(this.saveContractor).click({ force: true })
        cy.wait(6000)
      }

    })

  }

  importContractors(filePath) {
    cy.wait(2000)
    cy.get(this.manageContractors).click();
    cy.get(this.button).contains("Import Contractors").click();
    cy.get(this.button).contains("Skip").click() //step 1
    cy.wait(3000)
    // cy.contains('Select Files').selectFile(filePath); //upload the selected file
    cy.contains('Select Files').selectFile(['cypress/fixtures/images/W9_DATA_IMPORT_TEMPLATE1.csv'], { action: 'drag-drop' });
    cy.wait(2000);
    cy.get(this.button).contains("Next").click(); //step2

    cy.get(this.button).contains("Import Data").click(); //step3

    let contratorCount = null
    cy.get('.contractor-review-count') //step4. Checking number of contractors for import
      .then(($el) => {
        contratorCount = $el.text().slice(0, 1)
        cy.log(contratorCount)
        for (var i = 0; i < contratorCount; i++) {  //Reviewing the all contractors 1 by 1
          cy.get(this.button).contains("Save Changes").click({ force: true });
          cy.wait(2000)
        }
      })

    //all contractors are reviewed now
    cy.get('.contractor-verified-count') //taking contractor count for actual import
      .then(($el) => {
        contratorCount = $el.text().slice(0, 1)
        cy.log(contratorCount)
        cy.get(this.button).contains(`Save ${contratorCount} Contractors`).click({ force: true });
        cy.wait(3000);
        //cy.get('.MuiAlert-message').should('have.text', `${contratorCount} contractors were successfully imported.`);
      })
  }

  PayerEinAndAddressUpdate() {
    cy.wait(6000)
    //EIN update in Business Detail Page
    cy.get(this.extraOptionBtn).eq(0).click()
    cy.get(this.bizDetail).click()
    cy.get(this.button).contains('Edit').click()
    cy.get(this.ein).clear().type(*********)
    cy.get(this.button).contains("Save Changes").click()
    cy.wait(2000)

    //Address update at user level (Personal detail page)
    cy.get(this.extraOptionBtn).eq(8).click()
    cy.get(this.myDetails).contains("My Details").click({ force: true })

    //iframe handling
    const getIframeBody = () => {
      // get the iframe > document > body
      // and retry until the body element is not empty
      return cy
        .get('iframe[src="https://uat.1800accountant.com/portal-embed/#/profile/details?"]')
        .its('0.contentDocument.body').should('not.be.empty')
        // wraps "body" DOM element to allow
        // chaining more Cypress commands, like ".find(...)"
        // https://on.cypress.io/wrap
        .then(cy.wrap)
    }

    getIframeBody().find('#edit-details-btn').should('have.text', 'Edit').click()
    getIframeBody().find('input[id="address"]').clear().type("6th Ave floor", { force: true });
    getIframeBody().find('input[name="address.city"]').clear().type("Dallas", { force: true });
    getIframeBody().find('select[name="address.state"]').select('string:TX')
    getIframeBody().find('input[name="address.zip_code"]').clear().type("75001", { force: true });
    getIframeBody().find('button[type="submit"]').click()

  }

}

export default manageContractorPage