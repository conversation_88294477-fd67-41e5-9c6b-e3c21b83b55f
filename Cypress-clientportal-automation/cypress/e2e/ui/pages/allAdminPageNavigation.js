class alladminPages {

    selectDashboard() {
        return cy.get('a[href="#/dashboard"]');
    };

    selectClientCenter() {
        return cy.get('a[href="#/users"]').eq(0);
    };

    selectCalendar() {
        return cy.get('a[href="#/calendars"]');
    }
    selectMessageCenter() {
        return cy.get('a[href="#/conversations"]');
    };
    selectDocuments() {
        return cy.get('a[href="#/documents"]');
    }
    selectTaxAdvisory() {
        return cy.get('a[href="#/tax-advisory"]');
    }
    selectTaxCenter() {
        return cy.get('a[href="#/tax-prep"]');
    }
    selectBookkeeping() {
        return cy.get('a[href="#/bookkeeping"]');
    }
    selectMorePageOptions() {
        return cy.get('ul[class="dropdown-menu"]').eq(1); //three dots to view more pages
    }
    selectPayroll() {
        return cy.get('a[href="#/payroll"]');
    }
    selectEntityFormation() {
        return cy.get('a[href="#/entity_formation"]');
    }
    selectVendasta() {
        return cy.get('a[href="#/vendasta"]');
    }
    selectCases() {
        return cy.get('a[href="#/cases"]');
    }
    selectReminders() {
        return cy.get('a[href="#/reminders"]');
    }
    selectUsers() {
        return cy.get('a[href="#/accountants"]');
    }
    selectManagerView() {
        return cy.get('a[href="#/manager_view"]');
    }
     selectManagerViewNew() {
        return cy.get('a[href="#/manager_view_new"]');
    }
    selectReports() {
        return cy.get('a[href="#/reports"]');
    }
  
}

export default alladminPages