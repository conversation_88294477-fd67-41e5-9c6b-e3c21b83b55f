class documentsPage{

    businessDocumentUpload()
    {

        //Clicking on Documents - Payroll Folder for Business
        cy.wait(10000)
        cy.contains('Documents').click();
        cy.wait(3000)
        cy.contains('Miscellaneous').click();
        cy.wait(2000)
        cy.contains('Miscellaneous Documents').click();
        cy.wait(2000)
        //Clicking on Upload Button
        cy.wait(2000)
        //Using Select File Function to upload the file in Documents folder for Business 
        cy.contains('Select Files').selectFile(['cypress/fixtures/images/businessDocument.png'], { action: 'drag-drop' });
        cy.wait(10000)
        //Verifying if the file is uploaded
        cy.reload()
       // cy.get('[style="margin-left: 16px;"]').contains('businessDocument.png');
        cy.get('.MuiBox-root .cursor-pointer .MuiTypography-root').should('contain', 'businessDocument.png');
       // cy.wait(2000)

    }

    deleteDocument()
    {
        //Clicking on the document 
        cy.wait(2000)
       // cy.get('[style="margin-left: 16px;"]').contains('businessDocument.png').click();
       cy.get('.MuiBox-root .cursor-pointer .MuiTypography-root').contains('businessDocument.png').click();
        cy.wait(3000)
        //Clicking on Delete Button
        //cy.get('[class*=MuiIconButton-label]').eq(4).click();
        cy.get('button[data-cy="delete-document-button"]').click({ force: true });
        cy.wait(3000)
        // Confirming the Delete Option
        cy.get('[data-cy="confirm-delete-btn"]').click();

        
    }


    privateDocumentUpload()
    {
        //Clicking on Documents - Tax Return Folder for Private
        cy.wait(5000)
        cy.get('[href="#/documents"]').eq(0).click();
        cy.wait(3000)
        cy.get('[class*=MuiTreeItem-content]').eq(7).click();
        cy.wait(3000)
        cy.contains('Tax Year 2024').click();
        cy.wait(3000)
        cy.contains('Tax Return').click();
        cy.wait(3000)
        //Using Select File Function to upload the file in Documents folder for Private
        cy.contains('Select Files').selectFile(['cypress/fixtures/images/personalDocument.png'], { action: 'drag-drop' });
        //Verifying if the file is uploaded
        cy.wait(10000)
       // cy.get('[style="margin-left: 16px;"]').contains('personalDocument.png');
       cy.get('.MuiBox-root .cursor-pointer .MuiTypography-root').contains('personalDocument.png');
    }

    downloadDocument()
    {
        //Clicking on the document 
        cy.wait(2000)
        cy.get('.MuiBox-root .cursor-pointer .MuiTypography-root').contains('personalDocument.png').click();
        //Clicking on Drawer Menu for More Options
        cy.get('.MuiBox-root > :nth-child(2) > .MuiIconButton-label > .MuiSvgIcon-root').click();
        //Clicking on Download to Download the File
        cy.contains('Download').click();
        cy.wait(5000)

    }

    renameDocument()
    {
        //Clicking on Rename to Rename the File
        cy.contains('Rename').click();
        cy.wait(2000)
        //Editing the File Name by Adding 'Edited' text to the file Name
        cy.get('.MuiFormControl-root > .MuiInputBase-root > .MuiInputBase-input').type('Edited');
        //Saving the Document
        cy.contains('Save').click();
        cy.wait(2000)
        //Asserting if Document is Renamed
        cy.get('.MuiAlert-message').contains('Document Renamed');
    }

    moveDocument()
    {
        //Clicking on the document 
        cy.get('.MuiBox-root .cursor-pointer .MuiTypography-root').contains('personalDocumentEdited.png').click();
        //Clicking on Drawer Menu for More Options
        cy.get('.MuiBox-root > :nth-child(2) > .MuiIconButton-label > .MuiSvgIcon-root').click();
        //Clicking on Move Document Option
        cy.contains('Move Document').click();
        cy.wait(2000)
        //Selecting the New Path for the Document to be moved
        cy.contains('Permanent Files').click();
        cy.contains('EIN Letter').click();
        cy.wait(2000)
        cy.contains('Move Here').click();
        //Asserting if Document is moved
        cy.get('.MuiAlert-message').contains('Document Moved');


    }
    filtersDocument()
    {
        cy.contains('Filters').click();
        cy.wait(2000)
        cy.contains('All Time').click();
        cy.wait(2000)
        cy.contains('This Week').click();
        cy.wait(2000)
        cy.contains('Apply Filters').click();
        cy.wait(2000)
        cy.get('[class*=MuiChip-root]').eq(1).should('contain', 'This Week');
    }

}
export default documentsPage