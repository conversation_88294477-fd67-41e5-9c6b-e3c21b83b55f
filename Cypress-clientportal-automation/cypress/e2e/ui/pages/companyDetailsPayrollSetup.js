import ProductPurchase from "./productPurchase";

class CompanyDetails {

    // ##### Locators #####

    // Additional Products page
    username = 'input[name="username"]'
    password = 'input[name="password"]'
    personalDetails = ".MuiButton-label > .MuiSvgIcon-root" //right top button to view the personal detail/signout option
    signOut = '[data-cy="sign-out-btn"]'
    additionalProducts = '[data-cy="goto-products-btn"]'

    //Business Information page
    currentStep = ".MuiGrid-align-items-xs-flex-start > .MuiList-root"
    businessName = 'input[name="name"]'
    tradeName = 'input[name="trade_name"]'
    ein = 'input[name="ein"]'
    nextBtn = ".MuiButton-label > .MuiTypography-root"

    //Business Filing Address page
    street = 'input[name="street"]'
    streetL2 = 'input[name="street_line_2"]'
    city = 'input[name="city"]'
    stateDD = 'div[id="mui-component-select-state"]' // dropdown (DD)
    zip = 'input[name="zip_code"]'
    phoneNum = 'input[name="phone_number"]'

    //Payroll Schedule Page
    monthlyPayDayDD = "#mui-component-select-day_1"
    ScheduleStartDateDD = "#mui-component-select-anchor_pay_date"
    StartDateVal = '#menu-anchor_pay_date > .MuiPaper-root > .MuiList-root > [tabindex="0"] > div'

    //Federal Tax Information page
    businesstypeDD = '#mui-component-select-tax_payer_type'
    federalFormDD = '#mui-component-select-filing_form'
    federalForm = '[data-value="944 (Annual federal tax return)"]'

    //Business Industry Page
    industryDD = '#mui-component-select-title'

    // Banking Information Page
    addBankAccount = '.MuiGrid-root'
    rountingNum = 'input[name="routing_number"]'
    AccountNum = 'input[name="account_number"]'
    AccountType = 'input[value="Savings"]'
    saveAccount = ".MuiButton-label"
    userAccessToken = 'portal_r.access_token'
    businessId = 'portal_r.current_business_id'
    deposit_1 = 'input[name="deposit_1"]'
    deposit_2 = 'input[name="deposit_2"]'
    submitDeposit = "button[type='submit']"

    //payrollSignatoryPage 
    ssn = 'input[name="ssn"]'
    fisrtName = 'input[name="first_name"]'
    lastName = 'input[name="last_name"]'
    email = 'input[name="email"]'
    phone = '[name="phone"]'
    title = 'input[name="title"]'
    birthdate = 'input[name="birthday"]'

    street1 = 'input[name="street_1"]'
    street2 = 'input[name="street_2"]'
    zip1 = 'input[name="zip"]'

    // electronicSignaturePage 
    agreeEsign = 'input[name="agreee"]'
    digitallysign_directDepositeForm = '[data-cy="company_direct_deposit-form-sign-button"] > .MuiButton-label'
    digitallySign_8655Form = '[data-cy="US_8655-form-sign-button"] > .MuiButton-label'
    submitPayroll = '[data-cy="submit-payroll-popup"]'
    viewEmpBtn = '[data-cy="view-emp-btn"] > .MuiButton-label'


    businessSetup(payrollData, creditCardDetails) {

        cy.contains('Dashboard').click()
        const purchase = new ProductPurchase()
        purchase.purchasePayrollProduct(creditCardDetails)

        cy.contains('Dashboard').click().
            then(() => {
                cy.window().then(win => {
                    const accessTokem = win.localStorage.getItem(this.userAccessToken).slice(1, -1)
                    cy.request({
                        method: 'GET',
                        url: `https://${payrollData.environment}.1800accountant.com/cbapi/app.php/api/user/current`,

                        headers: {
                            'Authorization': `Bearer ${accessTokem}`,
                        }, failOnStatusCode: false

                    }).then(response => {
                        const userName = response.body.username
                        cy.log("username: " + userName)
                        cy.get(this.personalDetails).eq(0).click();
                        cy.get(this.signOut).click();
                        cy.get(this.username).type(userName)
                        cy.get(this.password).type(payrollData.password)
                        cy.contains('Sign In').click()
                        cy.contains('Dashboard').click()
                    })

                })
            })

        cy.contains('Payroll').click();
        cy.contains('Company Details').click();
        cy.contains('Next').click();

        cy.get(this.currentStep).contains('Business Information').click();
        this.businessInformationPage(payrollData);

        cy.get(this.currentStep).contains('Terms of Service').click();
        this.termsOfServicePage();

        cy.get(this.currentStep).contains('Business Filing Address').click();
        this.businessFillingAddressPage(payrollData);

        cy.get(this.currentStep).contains('Payroll Schedule').click();
        this.payrollSchedulePage(payrollData);

        cy.get(this.currentStep).contains('Federal Tax Information').click();
        this.federalTaxInformationPage(payrollData);

        cy.get(this.currentStep).contains('Business Industry').click();
        this.businessIndustryPage(payrollData);

        cy.get(this.currentStep).contains('Banking Information').click();
        this.bankingInformationPage(payrollData);

        cy.get(this.currentStep).contains('Payroll Signatory').click();
        this.payrollSignatoryPage(payrollData);

        cy.get(this.currentStep).contains('Document Upload').click();
        this.documentUploadPage();

        cy.wait(6000);
        cy.get(this.currentStep).contains('Electronic Signatures').click();
        this.electronicSignaturePage();

    }


    businessInformationPage(payrollData) {
        cy.get(this.businessName).type(payrollData.businessName)
        cy.get(this.tradeName).type(payrollData.tradeName)
        cy.get(this.ein).type(this.randomEinNum())
        cy.get(this.nextBtn).contains('Next').click({ force: true });
    }
    // Function to generate random 9 digit ein number
    randomEinNum() {
        var randomNum = "";
        var possible = "**********";
        for (var i = 0; i < 9; i++)
            randomNum += possible.charAt(Math.floor(Math.random() * possible.length));
        return randomNum;
    }

    termsOfServicePage() {
        cy.contains('Accept Terms and Conditions').click();
        cy.get(this.nextBtn).contains('Next').click({ force: true });
    }

    businessFillingAddressPage(payrollData) {
        cy.get(this.street).clear().type(payrollData.street)
        cy.get(this.streetL2).clear().type(payrollData.streetL2)
        cy.get(this.city).clear().type(payrollData.city)
        cy.get(this.stateDD).click();
        cy.get(`#${payrollData.stateValue}`).click();
        cy.get(this.zip).type(payrollData.zip)
        cy.get(this.phoneNum).type(payrollData.phoneNum);
        cy.get(this.nextBtn).contains('Next').click({ force: true });
        cy.wait(6000)

    }

    payrollSchedulePage(payrollData) {
        cy.get(this.monthlyPayDayDD).click();
        cy.get(`#${payrollData.paydayVal}`).click();
        cy.get(this.ScheduleStartDateDD).click();
        cy.get(this.StartDateVal).click();
        cy.get(this.nextBtn).contains('Next').click({ force: true });
    }

    federalTaxInformationPage(payrollData) {
        cy.get(this.businesstypeDD).click();
        cy.get(`#${payrollData.businesstype}`).click();
        cy.get(this.federalFormDD).click();
        cy.get(this.federalForm).click();
        cy.get(this.nextBtn).contains('Next').click({ force: true });

    }

    businessIndustryPage(payrollData) {
        cy.get(this.industryDD).click();
        cy.get(`#${payrollData.industry}`).click();
        cy.get(this.nextBtn).contains('Next').click({ force: true });

    }

    bankingInformationPage(payrollData) {
        cy.get(this.addBankAccount).contains('Add Bank Account').click()
        cy.get(this.rountingNum).clear().type(payrollData.rountingNum)
        cy.get(this.AccountNum).clear().type(payrollData.AccountNum)
        cy.get(this.AccountType).click()
        cy.get(this.saveAccount).contains('Save').click()
        cy.get(this.currentStep).contains('Banking Information').click().
            then(() => {
                cy.window().then(win => {
                    //acccesstoken and businessId for GET and POST API call
                    const accessTokem = win.localStorage.getItem(this.userAccessToken).slice(1, -1)
                    const business_Id = win.localStorage.getItem(this.businessId).slice(1, -1)

                    cy.request({
                        method: 'GET',
                        url: `https://${payrollData.environment}.1800accountant.com/cbapi/app.php/api/business/${business_Id}/payroll_organizer/company/bank_account`,
                        //The GET call will provide us bank_account_uuid which we can use in POST call for bank verification 
                        headers: {
                            'Authorization': `Bearer ${accessTokem}`,
                        }, failOnStatusCode: false

                    }).then(response => cy.request({
                        method: 'POST',
                        url: `https://${payrollData.environment}.1800accountant.com/cbapi/app.php/api/business/${business_Id}/payroll_organizer/company/test_deposits`,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: {
                            'bank_account_uuid': response.body.bank_account_uuid

                        }, failOnStatusCode: false

                    }).then(response => {
                        //POST API call will return two deposite for bank verification
                        const depo1 = response.body.deposit_1
                        const depo2 = response.body.deposit_2
                        //Page reload after test_deposits API call for bank verification
                        cy.reload()
                        cy.get(this.deposit_1).clear().type(depo1)
                        cy.get(this.deposit_2).clear().type(depo2)
                        cy.get(this.submitDeposit).click();
                    })
                    )
                })
            })
    }

    payrollSignatoryPage(payrollData) {
        cy.get(this.ssn).type(payrollData.ssn)
        cy.get(this.fisrtName).clear().type(payrollData.fisrtName)
        cy.get(this.lastName).clear().type(payrollData.lastName)
        cy.get(this.email).clear().type(payrollData.email)
        cy.get(this.phone).clear().type(payrollData.phone)
        cy.get(this.title).clear().type(payrollData.title)
        cy.get(this.birthdate).clear().type(payrollData.birthdate)

        cy.get(this.street1).clear().type(payrollData.street1)
        cy.get(this.street2).clear().type(payrollData.street2)
        cy.get(this.city).clear().type(payrollData.city)
        cy.get(this.stateDD).click();
        cy.get(`#${payrollData.stateValue}`).click()
        cy.get(this.zip1).clear().type(payrollData.zip1)
        cy.get(this.nextBtn).contains('Next').click({ force: true });
        cy.wait(4000)

    }

    documentUploadPage() {
        cy.contains('Upload Documents').selectFile("cypress/fixtures/images/Img1.png"); //upload the selected file
        cy.wait(6000);
        cy.get(this.nextBtn).contains('Next').click({ force: true });
    }

    electronicSignaturePage() {
        cy.get(this.agreeEsign).click();
        cy.get(this.digitallysign_directDepositeForm).click();
        cy.get(this.digitallySign_8655Form).click();
        cy.get(this.submitPayroll).contains("Submit Payroll").click();
        cy.get(this.viewEmpBtn).click();
    }

}

export default CompanyDetails