class CreateAccount {

  firstName = 'input[name="firstName"]'
  lastName = 'input[name="lastName"]'
  email = 'input[name="email"]'
  password = 'input[name="password"]'
  confirmPassword = 'input[name="confirmPassword"]'
  phone = 'input[name="phoneN<PERSON>ber"]'
  businessName = 'input[name="businessName"]'
  createAccount = 'button[type="button"]'

  //Additional Business Details
  businessTypeDD = '#business_type'
  industryDD = '#industry'
  stateDD = '#form_state'
  dropdownListValue = '.MuiAutocomplete-option'
  GoToMyAccount = '.MuiButton-label'




  userAccountCreation(data) {
    //url
    cy.visit('https://qa1-a.1800accountant.com/sso/#/create-account')
    //User details
    // cy.get(this.firstName).type(`Mukesh${data}` + this.randomNum().slice(0,3))
    // cy.get(this.lastName).type(`Shrivastav${data}` + this.randomNum().slice(0,3))
    // cy.get(this.email).type(`ms${data}` + this.randomNum().slice(0,4) + `@qa1.com`)   
    cy.get(this.firstName).type(`Mukesh${data}`)
    cy.get(this.lastName).type(`Shrivastav${data}`)
    cy.get(this.email).type(`ms${data}` + `<EMAIL>`)
    cy.get(this.password).type('111111')
    cy.get(this.confirmPassword).type('111111')
    cy.get(this.phone).type(this.randomNum())
    cy.get(this.businessName).type(`ms${data} B1`)
    cy.get(this.createAccount).click()
    cy.wait(6000)

    //Next Page for business details 
    cy.get(this.businessTypeDD).click();
    cy.get(this.dropdownListValue).contains("C Corporation").click();
    cy.get(this.industryDD).click();
    cy.get(this.dropdownListValue).contains("Business").click();
    cy.get(this.stateDD).click();
    cy.get(this.dropdownListValue).contains("Alabama").click();
    cy.get(this.GoToMyAccount).contains('Go to my Account').click()

  }

  randomNum() {
    var randomNum = "";
    var possible = "**********";
    for (var i = 0; i < 10; i++)
      randomNum += possible.charAt(Math.floor(Math.random() * possible.length));
    return randomNum;
  }

}

export default CreateAccount