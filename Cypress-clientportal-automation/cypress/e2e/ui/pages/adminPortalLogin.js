class AdminPortalLogin {

  username = 'input[type="email"]'
  password = 'input[type="password"]'
  submit = 'button[type="submit"]'
  adminPortalHeader = 'a[class="connect-link"]'


  adminLogin(data) {
    cy.visit(Cypress.env('url') + "/admin/#/login")
    cy.get(this.username).type(data.username)
    cy.get(this.password).type(data.password)
    cy.wait(2000)
    this.googleRecaptchaCheck()
    cy.get(this.submit).click({ force: true })
    cy.wait(2000)
    cy.get(this.adminPortalHeader).should('be.visible')
    cy.get(this.adminPortalHeader).should('have.text', '1-800Accountant Connect')
  }

  googleRecaptchaCheck() {
    cy.get('iframe[title="reCAPTCHA"]').then($iframe => {
      const $body = $iframe.contents().find('body')
      cy.wrap($body)
        .find('#recaptcha-anchor')
        .should("be.visible")
        .click();
      cy.wait(2000)
    })
  };





}

export default AdminPortalLogin