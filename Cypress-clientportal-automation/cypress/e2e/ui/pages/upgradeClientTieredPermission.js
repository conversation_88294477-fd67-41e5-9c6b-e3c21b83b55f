import ProductPurchase from "./productPurchase";

class UpgradePortal {

    personalDetails = ".MuiButton-label > .MuiSvgIcon-root" //right top button to view the personal detail/signout option
    upgradePortalAcc = '[data-cy="goto-upgrade-portal-access-btn"]'
    upgradeNow = '.MuiButton-label'
    signOut = '[data-cy="sign-out-btn"]'
    userAccessToken = 'portal_r.access_token'
    username = 'input[name="username"]'
    password = 'input[name="password"]'
    dashboard = 'a[href= "#/dashboard"]'
    signIn = 'button[type="button"]'

    upgradePortalAccess(creditCardDetails) {
        cy.wait(6000)
        cy.get(this.dashboard).eq(0).click({ force: true })
        cy.wait(4000)
        cy.get(this.personalDetails).eq(0).click();
        cy.get(this.upgradePortalAcc).click({ force: true });
        this.clickIfExist(this.upgradeNow, creditCardDetails)
    }

    clickIfExist(element, creditCardDetails) {
        cy.get('body').then((body) => {
            cy.wait(5000).then(() => {
                if (body.find(element).text().includes('Upgrade Now')) {
                    cy.get(element).contains('Upgrade Now').eq(0).click({ force: true })
                    this.reloginAfterProductPurchase(creditCardDetails)
                } else {
                    cy.log('Upgrade button not found, The client already has platinum access')
                    cy.log('Skipping portal upgrade and proceeding for next TC run')
                    cy.get(this.dashboard).eq(0).click({ force: true })
                }
            })
        })
    }

    reloginAfterProductPurchase(creditCardDetails) {
        const purchase = new ProductPurchase()
        purchase.purchaseProduct(creditCardDetails)

        cy.get(this.dashboard).eq(0).click({ force: true }).
            then(() => {
                cy.window().then(win => {
                    const accessTokem = win.localStorage.getItem(this.userAccessToken).slice(1, -1)
                    cy.request({
                        method: 'GET',
                        url: `https://uat.1800accountant.com/cbapi/app.php/api/user/current`,

                        headers: {
                            'Authorization': `Bearer ${accessTokem}`,
                        }, failOnStatusCode: false

                    }).then(response => {
                        const userName = response.body.username
                        cy.log("username: " + userName)
                        cy.get(this.personalDetails).eq(0).click();
                        cy.get(this.signOut).click();
                        cy.get(this.username).type(userName)
                        cy.get(this.password).type("111111")

                        //code for google recaptcha click
                        cy.get("iframe")
                            .first()
                            .its("0.contentDocument.body")
                            .should("not.be.undefined")
                            .and("not.be.empty")
                            .then(cy.wrap)
                            .find("#recaptcha-anchor")
                            .should("be.visible")
                            .click();
                        cy.wait(2000);

                        cy.get(this.signIn).contains('Sign In').click()
                        cy.wait(6000)
                        cy.get(this.dashboard).eq(0).click({ force: true })
                    })
                })
            })

    }

}

export default UpgradePortal