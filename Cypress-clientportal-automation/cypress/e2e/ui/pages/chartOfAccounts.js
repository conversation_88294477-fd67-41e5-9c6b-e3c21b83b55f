import { eq } from "lodash";

class chartOfAccounts{

    //This method is adding New L4 Category along with creating a new segment
    addNewCategory()
    {
        cy.visit('/')
        cy.contains('Bookkeeping').click();
        cy.wait(5000)
        cy.get('[href="#/settings/chart-of-accounts"]').click();
        cy.get('[data-cy="manage-seg-btn"]').click();
        cy.get('#search-segments-field').type('Dummy Segment added');
        cy.get('[data-cy="add-new-segment-btn"]').click();
        cy.get('[data-cy="save-segment-name-btn"]').click();
        cy.get('[data-cy="cross-modal-close-btn"]').click();
        cy.wait(3000)
        cy.get('[data-cy="add-new-acc-btn"]').click();
        cy.get('#new-account-name').click();
        cy.get('#new-account-name').type('{enter}YP account');
        cy.get('#select-parent-account').click();
        cy.get('#select-parent-account').clear();
        cy.get('#select-parent-account').type('{enter}Accumulated Depreciation');
        cy.get('.MuiAutocomplete-option').contains('Accumulated Depreciation').click();
        cy.wait(3000)
        cy.get('[data-cy="save-new-cat-btn"]').click();
    
    }

        // runReport()
        // {   
        //     cy.wait(5000);
        //     cy.get('[data-cy="Accounts Receivable-open-more-btn""]').click();
        //    // cy.get('#simple-menu > .MuiPaper-root > .MuiList-root > [tabindex="0"]').click();
        // }
        //This method is updating newly added L4 Category
        editCategory()
        {
            cy.wait(5000)
            cy.get('[data-cy="yp-account-open-more-btn"]').click();
            cy.get('[data-cy="edit-segment"]').click();
            cy.get('#new-account-name').click().clear();
            cy.wait(3000)
            cy.get('#new-account-name').type('{enter}YP swiss account');
            cy.wait(3000)
            cy.get('[data-cy="save-new-cat-btn"]').click();
            cy.wait(3000)
    
        }
    // //This method is moving added L4 Category to another level 3 account
        moveCategory()
        {
            cy.reload()
            cy.wait(3000)
            cy.get('[data-cy="yp-swiss-account-open-more-btn"]').click();
            cy.wait(3000)
            cy.get('[data-cy="move-report"]').click();
            cy.wait(3000)
            cy.get('#destination').click();
            cy.wait(3000)
            cy.get('#menu-destination > .MuiPaper-root > .MuiList-root > [aria-disabled="false"]').click();
            //cy.get('[data-value="f49a2bd4-8105-4e8f-8a3c-f4e49215b67b"]').click();
            cy.wait(3000)
            cy.get('[data-cy="move-acc-btn"]').click();
            cy.wait(5000)

        }
    // //This method is deleting L4 Category
        deleteCategory()
        {
            cy.wait(3000)
            cy.get('[data-cy="yp-swiss-account-open-more-btn"]').click();
            cy.get('[data-cy="delete-segment"]').click();
            cy.get('[data-cy="delete-category-btn"]').click();
        }
        
    // runReportCategory()
    // {
    //     cy.wait(3000)
    //     cy.get('[data-cy="Demo account2-open-more-btn"]').click();
    //     cy.get('[]').click();
        


}
export default chartOfAccounts