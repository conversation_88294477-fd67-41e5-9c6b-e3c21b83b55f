class JournalentryPage {
    
    navigateje(){
        cy.wait(10000)
        //Click on Bookkepping option to expand all the module names showing in left navigation panel
        cy.contains('Bookkeeping').click();
        cy.wait(5000)
        //Click on Journal Entries option to navigate to the Journal entry details page
        cy.contains('Journal Entries').click({force:true});
        cy.wait(5000)
    }

    clickJournalentryButton(){
        //Click on Journal entry button and verify the Create a New Journal Entry pop up is displaying or not 
        cy.contains('Journal Entry').click();
        cy.wait(5000)
     }
    add(){
        //Add and save the journal entry by adding a category and credit/debit entries to it
        cy.get('.MuiAutocomplete-endAdornment').click();
        cy.get('input[placeholder="Category"]').type('Rent');
        cy.get('[class*=MuiAutocomplete-option]').eq(0).click();
        cy.get('.MuiGrid-grid-xs-5 > .MuiFormControl-root > .MuiInputBase-root > .MuiInputBase-input').type('126.00');
        cy.get(':nth-child(1) > .MuiGrid-grid-xs-6 > .MuiFormControl-root > .MuiInputBase-root > .MuiInputBase-input').click();
        cy.wait(5000)
        cy.get('input[placeholder="Category"]').eq(1).type('Telephone');
        cy.get('[class*=MuiAutocomplete-option]').eq(0).click();
        cy.wait(5000)
        cy.contains('Create Entry').click({force:true});
        cy.wait(5000)
    }
    edit(){
        //Edit and save the journal entry by adding a description to it
        cy.contains('Edit').click();
        cy.get(':nth-child(6) > .MuiInputBase-root').type('Dummy description');
        cy.contains('Save Changes').click({force:true});
        cy.wait(5000)
    }

    delete()
    {  //Delete the created journal entry by clicking on the delete button followed by the delete entries button.
        cy.contains('Delete').click();
        cy.contains('Delete Entries').click()


    }


  }

  export default JournalentryPage