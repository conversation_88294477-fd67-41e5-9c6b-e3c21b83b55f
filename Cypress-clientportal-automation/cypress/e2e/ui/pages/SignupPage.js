  class SignupPage {

    
    navigate(url){
         cy.visit(url)
    }
    
    email(){
      const emailid = () => Cypress._.random(0, 1e6)
      const id = emailid()
      const testname = `demouser ${id}`
      return cy.get('[title="Email"] > .form-group > .form-control').type(testname);
      
    }

    password(){
      return cy.get('[title="Password"] > .form-group > .form-control')
      
    }

    repeatpassword(){
      return cy.get('[title="Password Confirmation"] > .form-group > .form-control')
     }
    firstname(){
      return cy.get('#first_name')
     }
    lastname(){
      return cy.get('#last_name')
     }

    phone(){
      const uuid = () => Cypress._.random(0, 1e6)
      const id = uuid()
      const testname = `testname${id}`
      return cy.get('#phone').type(testname);
     }

    checkbox(){
      cy.get('.ui-checkbox').click()
     }

    clickSignup(){
      cy.get('.btn').click()
      cy.wait(10000)
     }

     clickSignin(){
      cy.wait(10000)
      cy.get('form.ng-untouched > .btn').click()
     // cy.contains('sign in').click()
      cy.wait(10000)
    
     }

    businessNameInput() {    
      const bizname = () => Cypress._.random(0, 1e6)
      const id = bizname()
      const automationName = `Automation Business ${id}`
      cy.get('input[name="name"]').type(automationName);
      
     }

    businessType() {
    cy.get('#mui-component-select-business_type').click();
    cy.get('[data-value="S"] > div').click();
     }

    businessIndustry() {
    cy.get('#mui-component-select-industry').click();
    cy.get('[data-value="BA"]').click();
     }

     businessformation(){
      cy.get('#mui-component-select-form_state').click();
      cy.get('[data-value="CA"]').click();
      cy.wait(10000)
     }

     proceedToPortalBtn() {
      cy.contains('Proceed to Portal').click();
      cy.wait(5000)
      cy.url().should('eq', 'https://uat.1800accountant.com/portal/#/dashboard')
      cy.contains('Skip the tour').click();
     // cy.contains('Additional Products').should('have.text', 'Additional Products')
     }

  }

  export default SignupPage