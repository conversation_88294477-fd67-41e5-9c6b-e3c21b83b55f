class mainNavigationControls {

    selectNotificationsModule() {
        return cy.get('.MuiButtonBase-root .MuiListItemText-root .MuiTypography-root');
    }

    selectTeamModule() {
        return cy.get('.MuiButtonBase-root .MuiListItemText-root .MuiTypography-root');
    }

    selectDashboardModule() {
        return cy.get('a[data-cy="portal-section-child-Dashboard"]')
    }

    selectDocumentsModule() {
        return cy.get('a[href="#/documents"]')
    }

    selectCommunicationsModule() {
        return cy.get('a[data-cy="portal-section-child-Communications"]')
    }

    selectCalenderModule() {
        return cy.get('a[href="#/calendar"]')
    }

    selectBookkeepingSubSection() {
        return cy.get('div[data-cy="expandable-sections-Bookkeeping"]')
    }

    selectTransactionsModule() {
        return cy.get('a[href="#/transactions"]')
    }

    selectJournalEntriesModule() {
        return cy.get('a[href="#/journal"]')
    }

    selectBankingModule() {
        return cy.get('a[data-cy="expandable-section-child-Banking"]')
    }

    selectChartOfAccountsModule() {
        return cy.get('a[href="#/settings/chart-of-accounts"]')
    }

    selectReconciliationsModule() {
        return cy.get('a[href="#/reconcile"]')
    }
    
    selectReportsModule() {
        return cy.get('a[href="#/reports"]')
    }

    selectReceiptsScanModule() {
        return cy.get('a[href="#/receipts-scan"]')
    }

    selectInvoicingSection() {
        return cy.get('div[data-cy="expandable-sections-Invoicing"]')
    }

    selectInvoicesModule() {
        return cy.get('a[href="#/invoices"]')
    }

    selectContactsModule() {
        return cy.get('a[href="#/contacts"]')
    }

    selectProductsServicesModule() {
        return cy.get('a[href="#/products"]')
    }

    selectManageCategoriesSection() {
        return cy.get('a[href="#/categories"]')
    }

    selectTaxesSection() {
        return cy.get('div[data-cy="expandable-sections-Taxes"]')
    }

    selectBusinessTaxInformationModule() {
        return cy.get('a[href="#/tax-organizer/business"]')
    }

    selectPersonalTaxInformationModule() {
        return cy.get('a[href="#/tax-organizer/personal"]')
    }

    selectEstimatedTaxesModule() {
        return cy.get('a[href="#/estimated-tax-organizer"]')
    }

    selectIssue1099Section() {
        return cy.get('div[data-cy="expandable-sections-Issue 1099"]')
    }

    selectFile1099NECModule() {
        return cy.get('a[href="#/contractors/file-1099-nec"]')
    }

    selectManage1099ContractorsModule() {
        return cy.get('a[href="#/contractors/manage"]')
    }

    selectMileageLogSection() {
        return cy.get('div[data-cy="expandable-sections-Mileage Log"]')
    }

    selectTripsModule() {
        return cy.get('a[href="#/mileage/trips"]')
    }

    selectVehiclesModule() {
        return cy.get('a[href="#/mileage/vehicles"]')
    }

    selectLocationsModule() {
        return cy.get('a[href="#/mileage/locations"]')
    }

    selectPurposesModule() {
        return cy.get('a[href="#/mileage/purposes"]')
    }

}

export default mainNavigationControls