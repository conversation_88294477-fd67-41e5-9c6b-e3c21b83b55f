class MessagesPage{

    startNewConversation() {
        cy.contains('Dashboard').click();
        cy.wait(6000)
        cy.contains('Messages').click();
        cy.wait(6000)
        //To check if any conversation exist on page
        cy.get('body').then(($body) => {
            if ($body.text().includes('New Message')) {
              cy.contains('New Message').click();
              cy.wait(2000)
              
            } else {
              cy.contains('START CONVERSATION').click();
              cy.wait(2000)
            }
        })

    }

    addSubject(msgdata) {
        return cy.get('input[name="subject"]').type(msgdata.subject)
    }

    selectRecipient() {
        cy.get('#mui-component-select-topic_id').click()   
        //return cy.get('[data-value="61e7e64146e12b5f3d5714d2"] > div') 
        return cy.get('[tabindex="0"] > div').contains('New Topic').click()
    }

    writeTheMessage(msgdata) {
       return cy.get('textarea[name="body"]').type(msgdata.description) 
    }

    sendMsg() {
        return cy.get('.MuiButton-label').contains('Send').click();
    }

    replyToExistingMessages(msgdata) {
        cy.wait(6000)
        this.viewMsgDetails(msgdata);

        //To check if we have any conversation for the searched subject 
        cy.get('body').then(($body) => {
            if ($body.text().includes('No Conversations Were Found')) {
                cy.get('#scrollableDiv').should('have.text','No Conversations Were Found').end();  
                cy.log("No Conversations Were Found")                                         
              
            } else {
                cy.get('.infinite-scroll-component').eq(0).click()//select the 0th index message from result list
                cy.wait(4000)
                cy.get('.MuiFormControl-root > .MuiInputBase-root > .MuiInputBase-input').type(msgdata.description) //add description
                cy.get('.MuiBox-root > .MuiGrid-root > :nth-child(2)').click(); //send
            }
            
        })
              
    }


    docUploadInMsg(msgdata,filePath1) {
        this.viewMsgDetails(msgdata)
       
        //To check if we have any conversation for the searched subject 
        cy.get('body').then(($body) => {
            if ($body.text().includes('No Conversations Were Found')) {
                cy.log("I am in If loop")
                cy.get('#scrollableDiv').should('have.text','No Conversations Were Found').end();  
                cy.log("No Conversations Were Found")                                         
              
            } else {
                cy.get('.infinite-scroll-component').eq(0).click()//select the 0th index message from result list
                cy.wait(4000)
                cy.contains('Upload Document').selectFile(filePath1); //upload the selected file
                cy.wait(6000);
            }
            
        })     
        
    }

    viewArchiveMsg() {
        cy.contains('Dashboard').click();
        cy.wait(6000)
        cy.contains('Messages').click();
        cy.wait(6000)
        //to verify if the module has some existing messages
        cy.contains('New Message').should('have.text', 'New Message');
        cy.wait(2000)
        cy.contains('Archived').click();
        cy.wait(4000);
        cy.get('#archived').click({multiple: true})
        cy.wait(6000)

        
    }
  

    //Separate function for common code
    viewMsgDetails(msgdata) {
        cy.contains('Dashboard').click();
        cy.wait(6000)
        cy.contains('Messages').click();
        cy.wait(4000)
        //to verify if the module has some existing messages
        cy.contains('New Message').should('have.text', 'New Message');
        cy.wait(2000)
        //to search for a specific subject
        cy.get('.MuiInputBase-input').type(msgdata.searchSubject).type('{enter}');
        cy.wait(4000)
        
       
    }
}

export default MessagesPage;