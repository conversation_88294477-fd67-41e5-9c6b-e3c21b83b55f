class ConnectedBankPage{

    connectedBank()
    {
    cy.visit('/')
    cy.wait(5000)
    cy.contains('Bookkeeping').click();
    cy.wait(5000)
    cy.contains('Banking').click();
    cy.wait(5000)
    cy.get('body').then(($body) => {
        if ($body.text().includes('Add Account')) {
          cy.contains('Add Account').click();
          cy.wait(5000)
          cy.get(':nth-child(5) > .MuiButtonBase-root').click();
          cy.wait(10000)
        } else {
          cy.contains('Connect my Bank').click();
          cy.wait(10000)
        }
    }) 
    cy.get('iframe').iframe().as('iframeContent')
    cy.get('@iframeContent').then((iframeContent) => {
    cy.get(iframeContent).find('input[name="Search for your bank"]').type("FinBank",{force: true});
    cy.get(iframeContent).find('[alt="FinBank"]').click();
    cy.get(iframeContent).find('[class*=continue-btn]').click();
    cy.get(iframeContent).find('input[name="Banking Userid"]').type("Demo",{force: true});
    cy.get(iframeContent).find('input[name="Banking Password"]').type("Go",{force: true});
    cy.get(iframeContent).find('[class*=sign-in]').click();
    cy.wait(5000)
    cy.get(iframeContent).find('[id=AutoLoan]').click();
    cy.get(iframeContent).find('[id=HomeMortgage]').click();
    cy.get(iframeContent).find('[id=Checking]').click();
    cy.get(iframeContent).contains('Save').click();
    cy.wait(3000)
    cy.get(iframeContent).contains('Submit').click();
    cy.wait(10000)
})
cy.get('.MuiDialogActions-root > .MuiButtonBase-root').click();
    }
    
    

}
export default ConnectedBankPage