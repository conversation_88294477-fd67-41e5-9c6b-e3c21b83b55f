import additionalProductsPage from "./additionalProductsPage";

class businessVto {
    checkbox = 'input[type="checkbox"]'
    ein = 'input[id="business.ein"]'
    businessaddress = '[data-cy="business.address.address"]'
    city = '[data-cy="business.address.city"]'
    businesstype = '[data-cy="business.business_type"]'
    state = '[data-cy="business.address.state"]'
    //Timezone as per state index value PST-California(4), EST-New York (32), CST-Texas (44)
    statedrop = 'li[data-option-index="4"]'  
    businesstate = '[data-cy="business.form_state"]'
    businesstatedrop = 'li[data-option-index="4"]'
    zipcode = '[data-cy="business.address.zip_code"]'
    businessformdate = '[data-cy="business.form_date"]'
    description = '[data-cy="business.description"]'
    sosnumberPartnership = 'input[name="business.tax_id_fields.0.CA_PS_california_sos_number"]'
    sosnumberScorp = 'input[name="business.tax_id_fields.0.CA_S_california_sos_number"]'
    sosnumberCcorp = 'input[name="business.tax_id_fields.0.CA_C_california_sos_number"]'
    taxpayment = 'input[value="false"][name="business.has_tax_payment_2024_california_franchise"]'
    franchisetax = 'input[value="false"][name="business.is_required_to_pay_franchise_tax_annual_filling_fee"]'
    numberofshares = 'input[id="number_of_shares"]'
    sharePercent = 'input[id="share_percent"]'
    ssn = '[data-cy="ssn"]'
    owneraddress = '[data-cy="business.owners.0.address.address"]'
    ownercity = '[data-cy="business.owners.0.address.city"]'
    ownerstate = '[data-cy="address.state"]'
    ownerstatedrop = 'li[data-option-index="4"]'
    ownerzipcode = '[data-cy="address.zip_code"]'
    ownerdob = '[data-cy="dob_date"]'
    owneruin = '[data-cy="owner_uin"]'
    owneruindrop = 'ul li[data-value="us_passport"]'
    ownerpassport = '[data-cy="business.owners.0.passport_number"]'
    vehicle = 'input[value="false"][name="business.has_automobiles"]'
    homeoffice = 'input[value="false"][name="business.has_home_expenses"]'
    employee = 'input[value="false"][name="business.has_employees"]'
    balancesheet = '[data-cy="business.software_for_bookkeeping_other"]'
    balancesheetcount = 'input[value="1"][name="forms_count.balance_sheet"]'
    balancesheetprofit = 'input[value="1"][name="forms_count.balance_profit"]'
    estimatedpayments = 'input[value="false"][name="business.misc_any_estimated_payments"]'
    foreigninvolve = 'input[value="false"][name="business.misc_foreign_involve"]'
    requireddoc = 'input[name="select-all"]'
    comments = 'textarea[name="submitDetails.additional_info"]'
    agreecheckbox = 'input[name="submitDetails.submit_confirm"]'
    fullname = 'input[name="submitDetails.consent_disclosed_by"]'



    //This method is for BTP page
    businessTaxPrepAp() {
        cy.wait(3000)
        cy.viewport(1920, 1080);
        cy.contains('Taxes').click();
        cy.contains('Business Tax Information').click();
        cy.get(this.checkbox).click();
        cy.contains('Get Started').click();
        cy.contains('span', 'Next').click();
        // cy.get(this.checkbox).click(); //this page is dynamic and available from 1st jan- 15th april
        // cy.contains('p', 'Continue To My Draft Tax Return').click();
        cy.contains('button', 'Other').click();
        cy.contains('span', 'Next').click();
        cy.contains('p', 'Business Entity Type').click();

        let bizType = 'Partnership' // C Corporation , S Corporation, Partnership
        cy.get(this.businesstype).eq(0).click();
        this.businessType(bizType);
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.ein).type('*********');
        cy.get(this.businessaddress).first().type('Dummy address');
        cy.get(this.city).first().type('Dummy city');
        cy.get(this.state).eq(1).click();
        cy.get(this.statedrop).click({ force: true });
        cy.get(this.zipcode).eq(1).type('777778');
        cy.get(this.businessformdate).first().type('01/01/2024');
        cy.get(this.businesstate).eq(0).click();
        cy.get(this.businesstatedrop).click({ force: true });
        cy.get(this.description).first().type('dummy description');
        this.californiaSOSNumber(bizType);
        cy.get(this.taxpayment).click({ force: true });
        cy.get(this.franchisetax).click({ force: true });
        cy.contains('Button', 'Confirm & Continue').click();
        if (bizType == "Partnership") {
            cy.get(this.sharePercent).clear().type('100');

        } else {
            cy.get(this.numberofshares).clear().type('10');
        }
        cy.get(this.ssn).eq(1).type('*********789');
        cy.get(this.owneraddress).first().type('Dummy owner address');
        cy.get(this.ownercity).first().type('Dummy owner city');
        cy.get(this.ownerstate).eq(1).click();
        cy.get(this.ownerstatedrop).click({ force: true });
        cy.get(this.ownerzipcode).eq(1).type('123456');
        cy.get(this.ownerdob).first().type('11/11/2024');
        cy.get(this.owneruin).eq(1).click();
        cy.get(this.owneruindrop).click({ force: true });
        cy.get(this.ownerpassport).first().type('1122334455');
        cy.contains('Upload File').selectFile(['cypress/fixtures/images/W9_DATA_IMPORT_TEMPLATE1.csv'], { action: 'drag-drop' });
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.vehicle).click({ force: true })
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.homeoffice).click({ force: true })
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.employee).click({ force: true })
        cy.contains('Button', 'Confirm & Continue').click();
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.balancesheet).first().type('For test only');
        cy.get(this.balancesheetcount).click({ force: true });
        cy.contains('Upload File').selectFile(['cypress/fixtures/images/W9_DATA_IMPORT_TEMPLATE1.csv'], { action: 'drag-drop' });
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.balancesheetprofit).click({ force: true });
        cy.contains('Upload File').selectFile(['cypress/fixtures/images/W9_DATA_IMPORT_TEMPLATE1.csv'], { action: 'drag-drop' });
        cy.contains('Button', 'Confirm & Continue').click();
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.estimatedpayments).click({ force: true });
        cy.get(this.foreigninvolve).click({ force: true });
        cy.contains('Button', 'Confirm & Continue').click();
        cy.get(this.requireddoc).eq(0).click();
        cy.get(this.requireddoc).eq(1).click();
        cy.contains('Button', 'Submit Your Tax Info').click();
        cy.get(this.comments).first().type('Dummy additional info');
        cy.get(this.agreecheckbox).click({ force: true });
        cy.get('#consent-form').scrollTo('bottom').should('be.visible');
        cy.get(this.fullname).first().type('Demo Test User');
        cy.contains('Button', 'Next').click();
        cy.contains('Button', 'No Thanks').click();
        cy.contains('Button', 'Submit Your Tax Info').click();
        const pp = new additionalProductsPage();
        pp.purchaseProduct();
        cy.wait(5000)
        cy.contains('Button', 'dashboard').click();
        cy.reload();
    }

    businessType(bizType) {
        if (bizType == "Partnership") {
            cy.get('ul[role="listbox"]').find(`li[id=${bizType}]`).click({ force: true });

        } else {
            cy.get('ul[role="listbox"]').contains(bizType).click({ force: true });
        }
    }

    californiaSOSNumber(bizType) {
        if (bizType == "Partnership") {
            cy.get(this.sosnumberPartnership).first().type('123456');
        } else if (bizType == "S Corporation") {
            cy.get(this.sosnumberScorp).first().type('123456');
        } else {
            cy.get(this.sosnumberCcorp).first().type('123456');
        }

    }

}

export default businessVto