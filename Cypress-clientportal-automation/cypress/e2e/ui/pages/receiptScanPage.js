class recieptScanPage{

    receiptScan()
    {
        //Navigating to Receipt Scan Page
        cy.visit('/')
        cy.wait(2000)
        cy.contains('Bookkeeping').click();
        cy.contains('Receipts Scan').click();
        cy.wait(2000)
        //Dragging Multiple Files in Select File Section
        cy.contains('Select Files').selectFile(['cypress/fixtures/images/Receipt.jpeg','cypress/fixtures/images/receipt2.png'], { action: 'drag-drop' });
        cy.wait(3000)
        //Clicking Scan button
        cy.get('[class*=MuiButton-contained]').eq(1).click();
        cy.wait(10000)
        //Clicking Checknox to create Journal Entries
        cy.get(':nth-child(2) > .MuiFormControlLabel-root > .MuiTypography-root').click();
        cy.wait(5000)
        //Clicking Next for Second Receipt
        cy.contains('Next').click();
        cy.wait(3000)
        //Selecting Category for Second Receipt
        cy.get('.MuiAutocomplete-root > .MuiFormControl-root > .MuiInputBase-root').click();
        cy.wait(2000)
        cy.contains('Utilities').click();
        cy.wait(2000)
        //Clicking on Create button to create journal entries of scanned receipt
        cy.contains('Create').click();
        cy.wait(5000)
        //Verifying if Journal Entries are created
        cy.get('.MuiSnackbar-root > .MuiPaper-root').contains('Journal Entries added.');
    }


}
export default recieptScanPage