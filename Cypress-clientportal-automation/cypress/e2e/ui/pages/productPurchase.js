class ProductPurchase {

    // Product purchase
    personalDetails = ".MuiButton-label > .MuiSvgIcon-root" //right top button to view the personal detail/signout option
    signOut = '[data-cy="sign-out-btn"]'
    additionalProducts = '[data-cy="goto-products-btn"]'
    labelPayroll = '.MuiContainer-fixed > .MuiList-root > :nth-child(1)'
    checkout = '.MuiContainer-root > .MuiButtonBase-root'

    creditCardNumber = 'input[name="creditcard[crypt_number]"]'
    creditCardExpiryMonth = 'input[name="creditcard[expdate_month]"]'
    creditCardExpiryYear = 'input[name="creditcard[expdate_year]"]'
    creditCardCVV = 'input[name="creditcard[cvv]"]'
    nameOnCreditCard = 'input[name="creditcard[crypt_name]"]'
    creditCardStreetAddr = 'input[name="creditcard[crypt_address]"]'
    creditCardCity = 'input[name="creditcard[crypt_city]"]'
    creditCardState = 'input[name="creditcard[crypt_state]"]'
    creditCardPostalcode = 'input[name="creditcard[crypt_postcode]"]'
    creditCardCountryDD = 'div[id="creditcard_crypt_country_chosen"]'   // dropdown (DD)
    creditCardCountry = 'div[class="chosen-search"]'
    purchaseSuccessMsg = '.MuiAlert-message'

    purchasePayrollProduct(purchase) {
        cy.get(this.personalDetails).eq(0).click(); //right top button to view the personal detail/signout option
        cy.get(this.additionalProducts).click();
        cy.get(this.labelPayroll).contains('Payroll').click();
        cy.wait(3000);
        cy.get(this.checkout).contains('button', 'Checkout').click();
        this.purchaseProduct(purchase);
    }

    purchaseProduct(purchase) {
        cy.wait(30000);
        cy.get('iframe').iframe().as('iframeContent');
        cy.get('@iframeContent').then((iframeContent) => {
            cy.get(iframeContent).contains('Complete order now').click();
            cy.wait(10000);
            cy.get('iframe').iframe().as('iframeContent');
            cy.get('@iframeContent').then((iframeContent) => {
                cy.get(iframeContent).find(this.creditCardNumber).type(purchase.creditCardNumber, { force: true });
                cy.get(iframeContent).find(this.creditCardExpiryMonth).type(purchase.creditCardExpiryMonth, { force: true });
                cy.get(iframeContent).find(this.creditCardExpiryYear).type(purchase.creditCardExpiryYear, { force: true });
                cy.get(iframeContent).find(this.creditCardCVV).type(purchase.creditCardCVV, { force: true });
                cy.get(iframeContent).find(this.nameOnCreditCard).type(purchase.nameOnCreditCard, { force: true });
                cy.get(iframeContent).find(this.creditCardStreetAddr).clear().type(purchase.creditCardStreetAddr, { force: true });
                cy.get(iframeContent).find(this.creditCardCity).clear().type(purchase.creditCardCity, { force: true });
                cy.get(iframeContent).find(this.creditCardState).clear().type(purchase.creditCardState, { force: true });
                cy.get(iframeContent).find(this.creditCardPostalcode).clear().type(purchase.creditCardPostalcode, { force: true });
                cy.get(iframeContent).contains('Pay Now').click();
                cy.wait(10000);
               
            })
        })
    }

}

export default ProductPurchase