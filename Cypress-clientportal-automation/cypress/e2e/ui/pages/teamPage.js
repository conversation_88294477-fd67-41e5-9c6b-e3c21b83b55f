class TeamPage{
    //Team page elements

    schedulePayrollAppt(){
        cy.contains('Dashboard').click();
        cy.wait(4000)
        cy.contains('Team').click();
        cy.contains('Payroll Specialist').should('have.text', 'Payroll Specialist');
        cy.wait(3000)
           
        //To print a text value of an element in log (in our case its for payroll accountant assigned)
        cy.get('.MuiBox-root > :nth-child(1) > .MuiList-root > :nth-child(9)').invoke('text').then(cy.log)

        //Condition to handle if the payroll accountant is not assigned 
        let AccAssigned = null
        cy.get('.MuiBox-root > :nth-child(1) > .MuiList-root > :nth-child(9)')
        .then(($el) => {
            AccAssigned = $el.text()
            cy.log(AccAssigned)
            if(AccAssigned =='Learn MorePayroll Specialist'){    

                cy.log("Payroll Tax accountant not assigned")

                } else{
                    //Calender icon click
                    cy.get(':nth-child(9) > .MuiListItemSecondaryAction-root > :nth-child(2) > .MuiIconButton-label > img').click()
                    cy.contains('New Appointment').should('have.text', 'New Appointment')
                    cy.get('#mui-component-select-topic').contains('Select a Topic').click(); //DDLB click to see the available topics
                    //Payroll topic selection from the DDLB
                    cy.get('#menu-topic > .MuiPaper-root > .MuiList-root > [data-value="Payroll"]').click(); 
                    cy.get('.MuiButton-label').contains('Schedule Appointment').click();
                    cy.wait(6000)
                    //The calender date is automatically selected via FTP. we are just adding description in description field
                    cy.get('textarea[name="description"]').type('Test Appointment schedule')
                    cy.get('button[type="submit"]').click();
                    cy.wait(6000)    
                    
                }    
               
            }) 

        } 


}

export default TeamPage;