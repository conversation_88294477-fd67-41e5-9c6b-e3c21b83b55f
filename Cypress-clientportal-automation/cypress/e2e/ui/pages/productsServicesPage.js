class productsServicesPage{

     //This method is adding New Product
     addNewProduct()
     {
         cy.visit('/')
         cy.wait(10000)
         cy.contains('Bookkeeping').click();
         cy.wait(3000)
         cy.contains('Products / Services').click();
         cy.wait(3000)
         cy.contains('Add New').click();
         const productname = () => Cypress._.random(0, 1e6)
         const id = productname()
         const prodName = `Automation product ${id}`
         cy.get('input[name="title"]').type(prodName);
         cy.get('input[name="price"]').type('25');
         cy.wait(3000)
         cy.contains('Save Changes').click();
         cy.get('.MuiAlert-message').should('have.text', 'Product Created Successfully');
         cy.wait(3000)
     }

      //This method is adding New Service
    addNewService()
    {
        cy.wait(3000)
        cy.contains('Add New').click();
        cy.wait(3000)
        const servicename = () => Cypress._.random(0, 1e6)
        const id = servicename()
        const servName = `Automation Service ${id}`
        cy.get('input[name="title"]').type(servName);
        cy.get('[value="service"]').click();
        cy.get('input[name="price"]').type('30');
        cy.wait(3000)
        cy.contains('Save Changes').click();
        cy.wait(3000)
        cy.get('.MuiAlert-message').should('have.text', 'Product Created Successfully');
        cy.wait(3000)
    }
    //This method Edit the Product and enteres the Item ID
    searchEditProduct()
    {
       // cy.get('input[placeholder="Search"]').click().type('Automation Product').type('{enter}');
      //cy.get('.MuiInputBase-root').click().type('Automation Product').type('{enter}');
        cy.wait(3000)
        cy.get('.infinite-scroll-component > :nth-child(3)').click();
        cy.wait(3000)
        cy.contains('Edit').click();
        cy.wait(3000)
        cy.get('input[name="item_id"]').type('Automation Product ID');
        cy.wait(3000)
        cy.contains('Save Changes').click();
        cy.wait(3000)
    }
    //This method Edit the Service and enteres the Item ID
    searchEditService()
    {
        //cy.get('input[placeholder="Search"]').click().type('Automation Service').type('{enter}');
        cy.wait(3000)
        cy.get('.infinite-scroll-component > :nth-child(1)').click();
        cy.wait(3000)
        cy.contains('Edit').click();
        cy.wait(3000)
        cy.get('input[name="item_id"]').type('Automation Service ID');
        cy.wait(3000)
        cy.contains('Save Changes').click();
        cy.wait(3000)
    }
    //This method finds existing created Product & Deletes the Product Item
    deleteProduct()
    {
        cy.get('.infinite-scroll-component > :nth-child(1)').click();
        cy.contains('Delete').click();
        cy.wait(2000)
        cy.get('button[data-cy="delete-confirm-button"]').click();
        cy.get('.MuiAlert-message').should('have.text', 'Product Deleted Successfully');
        cy.wait(3000)
    }
     //This method finds existing created Service & Deletes the Service Item
     deleteService()
     {
         cy.reload();
        cy.wait(10000)
         cy.get('.infinite-scroll-component > :nth-child(1)').click();
         cy.contains('Delete').click();
         cy.wait(2000)
         cy.get('button[data-cy="delete-confirm-button"]').click();
         cy.get('.MuiAlert-message').should('have.text', 'Product Deleted Successfully');
         cy.wait(3000)
     }  
     //This method Test the functionality of creating a custom category for a product and then finding the product by Category search
     categoryTests()
     {
        cy.contains('Add New').click();
        cy.wait(3000)
        const rancatname = () => Cypress._.random(0, 1e2)
        const id = rancatname()
        const titleName =`Category Testing ${id}`
        const catName = `Category ${id}`
        cy.get('input[name="title"]').type(titleName);
        cy.get('input[name="price"]').type('40');
        cy.get('#product-category-selector').click();
        cy.get('button[id="create-new-category-button"]').click();
        cy.get('input[placeholder="Name"]').click().type(catName).type('{enter}');
        cy.wait(3000)
        cy.contains('Save Changes').click();
        cy.wait(3000)
        cy.reload()
        cy.wait(3000)
        cy.get('#category-select-options').type(catName);
        cy.wait(3000)
        cy.get('#category-select-options-option-0').click();
        cy.get('.infinite-scroll-component > .MuiListItem-root').click();
        cy.contains('Delete').click();
        cy.wait(2000)
        cy.get('button[data-cy="delete-confirm-button"]').click();
        cy.get('.MuiAlert-message').should('have.text', 'Product Deleted Successfully');
        cy.wait(3000)
        cy.get('#category-select-options').clear();

     }
}
export default productsServicesPage