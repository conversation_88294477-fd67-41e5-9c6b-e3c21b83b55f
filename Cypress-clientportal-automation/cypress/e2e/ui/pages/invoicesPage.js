class invoicesPage{

    addInvoice()
    {
        cy.visit('/')
         cy.wait(10000)
         cy.contains('Bookkeeping').click();
         cy.wait(3000)
         cy.contains('Invoices').click();
         cy.wait(3000)
         cy.contains('New Invoice').click();
         cy.get('input[name="contact_data"]').click();
         cy.contains('Create New Customer').click();
         cy.get('input[name="name"]').type('Automation Contact');
         cy.get('input[name="email"]').type('<EMAIL>');
         cy.wait(3000)
         cy.get('button[data-cy="save-contact-changes"]').click();
         cy.get('.MuiSnackbar-root > .MuiPaper-root > .MuiAlert-message').should('have.text', 'Contact created successfully');
         cy.get('.MuiGrid-grid-md-6 > .MuiAutocomplete-root > .MuiFormControl-root > .MuiInputBase-root').click();
         cy.contains('Create New Product').click();
         const productName = () => Cypress._.random(0, 1e6)
        const id = productName()
        const prodName = `Automation Service ${id}`
         cy.get('input[name="title"]').type(prodName);
         cy.get('input[name="price"]').type('40');
         cy.get('button[data-cy="save-product-changes"]').click();
         cy.contains('Save Changes').click();
    }

    updateInvoice()
    {
        cy.wait(3000)
        cy.get('.infinite-scroll-component > :nth-child(1) > .MuiListItem-root').click();
        cy.contains('Edit').click();
        cy.get('[rows="5"]').type('This is Automation Script');
        cy.contains('Update').click();
        cy.get('.MuiSnackbar-root > .MuiPaper-root > .MuiAlert-message').should('have.text','Invoice updated successfully');

    }

    deleteInvoice()
    {
        cy.get('.infinite-scroll-component > :nth-child(1) > .MuiListItem-root').click();
        cy.wait(3000)
        cy.contains('Delete').click();
        cy.get('button[data-cy="delete-invoice-btn"]').click();
        cy.get('.MuiSnackbar-root > .MuiPaper-root > .MuiAlert-message').should('have.text','Invoice deleted successfully');
    }
}
export default invoicesPage