class ManualbankPage{

manualBank()
{
    cy.visit('/')
    cy.wait(5000)
    cy.contains('Bookkeeping').click();
    cy.wait(2000)
    cy.contains('Banking').click();
    cy.wait(5000)
    cy.get('body').then(($body) => {
        if ($body.text().includes('Add Account')) {
          cy.contains('Add Account').click();
          cy.contains('Create a Manual Account').click();
        } else {
          cy.contains('Create a Manual Account').click();
        }
    })
}
bankNameInput()
{
    return cy.get('*[class*=MuiGrid-grid-md-12]')
}
bankType()
{
    cy.get('#mui-component-select-account_type').click();
    cy.get('[data-value="savings"]').click();

}
amount()
{
    return cy.get('*[class*=MuiGrid-grid-xs-true]')
}

nextBtn()
{
    cy.contains('Next').click();
}
bankDetails()
{
    const bankname = () => Cypress._.random(0, 1e6)
    const bName = bankname()
    const banName = `Automation Bank ${bName}`
    cy.get('[style="margin-top: 20px;"] > :nth-child(2) > .MuiFormControl-root > .MuiInputBase-root > .MuiInputBase-input').type(banName);
    cy.get(':nth-child(5) > .MuiFormControl-root > .MuiInputBase-root > .MuiInputBase-input').type(banName);
    cy.wait(2000)
}
createAccount()
{
    cy.contains('Create Account').click();
    cy.wait(2000)
    cy.get('.MuiAlert-message').should('have.text', 'Bank account created successfully');
}

editAccount()
{
    //Selecting the Created Bank Account [As this will be executed along with create bank account test case]
    cy.contains('Automation Bank').click();
    cy.contains('Edit').click();
    cy.wait(3000)
    //Appending the text 'Edited' in Account Name
    cy.get('*[class*=MuiGrid-grid-md-12]').eq(1).type(" Edited");
    cy.contains('Next').click();
    cy.wait(2000)
    cy.contains('Next').click();
    cy.wait(2000)
    //Updating the account
    cy.contains('Update account').click();
    cy.wait(2000)
    //Asserting if Bank account is updated successfully
    cy.get('.MuiAlert-message').should('have.text', 'Bank account updated successfully');

}
}
export default ManualbankPage