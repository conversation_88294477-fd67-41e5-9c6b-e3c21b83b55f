import additionalProductsPage from "./additionalProductsPage";

class adpPayroll
{
        // ##### Locators #####
         // Additional Products page
        username = 'input[name="username"]'
        password = 'input[name="password"]'
        personalDetails = ".MuiButton-label > .MuiSvgIcon-root" //right top button to view the personal detail/signout option
        signOut = '[data-cy="sign-out-btn"]'
        additionalProducts = '[data-cy="goto-products-btn"]'
        userAccessToken = 'portal_r.access_token'
    
        //Business Information page
        businessName = 'input[name="business_name"]'
        ein = 'input[name="ein"]'
        industry='[data-cy="business-industries"]'
        industrydrop='div.MuiAutocomplete-popper'
        businesstype='div#mui-component-select-business_type'
        businesstypedrop='ul li[data-value="Corporation"]'
        FilingFrequency='[data-cy="federal_filing_frequency"] input[type="radio"][value="Annual"]'
        DepositFrequency='[data-cy="deposit_frequency"] input[type="radio"][value="Monthly"]'
        location='input[placeholder="Search Location"]'
        street='[data-cy="street"] input[name="street"]'
        city='[data-cy="city"] input[name="city"]'
        state='[data-cy="state"]'
        statedrop='div.MuiAutocomplete-popper ul li[role="option"]'
        zipcode='[data-cy="zip_code"] input[name="zip_code"]'


        //Payroll Schedule Page
        payroll_frequency='[data-cy="frequency"] input[type="radio"][value="Monthly"]'
        first_payment_date= '[data-cy="first_payment_date"] input[name="first_payment_date"]'
        first_work_period_start_date='[data-cy="first_work_period_start_date"] input[name="first_work_period_start_date"]'
        first_work_period_end_date='[data-cy="first_work_period_end_date"] input[name="first_work_period_end_date"]'


        //Banking Information Page
        link_account='span.Thr-Button-Text'
        Routing_number='#device-input'
        continue='#aut-button span.Thr-Button-Text'
        account_number='input[placeholder="Account number"]'
        confirm_account_number='#account-number-confirmation.TextInput-module_input__vDQVM'
        full_name='input[placeholder="Full Name"]'
        authorize='#aut-button'
        Phone_number='#number-input'
        letter_code=' .TextInput-module_baseInputRoot__x9A2n #nonce-input[placeholder="3-letter code"]'
        submit='#aut-button-button_one_tap'


        //Owner Details
        first_name='[data-cy="first_name"] input[placeholder="First Name (Required)"]'
        last_name='[data-cy="last_name"] input[placeholder="Last Name (Required)"]'
        email='[data-cy="email"] input[placeholder="Email (Required)"]'
        dob='[data-cy="dob"] input[name="dob"]'
        phone='[data-cy="phone"] input[name="phone"]'
        owner_ssn='[data-cy="ssn"] input[name="ssn"]'
        gender='[data-cy="gender"] input[type="radio"][value="Female"]'
        is_owner_an_emplyee='[data-cy="is_owner_an_employee"] input[type="radio"][value="true"]'
        job_type='[data-cy="job_type"] #mui-component-select-job_type'
        hire_date='[data-cy="hire_date"] input[name="hire_date"]'
        job_title='[data-cy="job_title"] input[name="job_title"]'
        amount='[data-cy="amount"] input[name="amount"]'
        frequency='[data-cy="frequency"] #mui-component-select-frequency'
        percentage='[data-cy="ownership_percentage"] #ownership_percentage'


        //Agreements
        agree1='[data-cy="agreement_1"] input[type ="checkbox"]'
        agree2='[data-cy="agreement_2"] input[type ="checkbox"]'
        agree3='[data-cy="agreement_3"] input[type ="checkbox"]'
        agree4='[data-cy="agreement_4"] input[type ="checkbox"]'
        agree5='[data-cy="agreement_5"] input[type ="checkbox"]'
        agree6='[data-cy="agreement_6"] input[type ="checkbox"]'
        agree7='[data-cy="agreement_7"] input[type ="checkbox"]'


    
        businessSetup(ADPPayrollData, creditCardDetailsADP) {
            cy.contains('Dashboard').click()
           const purchase = new additionalProductsPage()
           purchase.purchasePayrollProduct(creditCardDetailsADP)
            cy.contains('Dashboard').click().
            then(() => {
                cy.window().then(win => {
                    const accessTokem = win.localStorage.getItem(this.userAccessToken).slice(1, -1)
                    cy.request({
                        method: 'GET',
                        url: `https://uat.1800accountant.com/cbapi/app.php/api/user/current`,

                        headers: {
                            'Authorization': `Bearer ${accessTokem}`,
                        }, failOnStatusCode: false

                    }).then(response => {
                        const userName = response.body.username
                        cy.log("username: " + userName)
                        cy.get(this.personalDetails).eq(0).click();
                        cy.get(this.signOut).click({ force: true });
                        cy.get(this.username).type(userName)
                        cy.get(this.password).type("111111");
                        cy.get('iframe[title="reCAPTCHA"]').then($iframe => {
                            const $body = $iframe.contents().find('body')
                            cy.wrap($body)
                              .find('#recaptcha-anchor')
                              .should("be.visible")
                            .click();
                            cy.wait(5000);
                          })
                        cy.get('button[type="button"]').contains('Sign In').click()
                        //cy.get("//button[normalize-space()='Sign In']").click({ force: true });s
                        cy.contains('Dashboard').click()
                        
                    })

                })
            })

            cy.contains('Payroll').click();
            cy.contains('Company Details').click();
            cy.wait(3000)
            this.businessInformationPage(ADPPayrollData);
    
        }
    
        businessInformationPage(ADPPayrollData) {
            cy.get(this.businessName).type(ADPPayrollData.businessName);
            cy.get(this.ein).type(this.randomEinNum());
            cy.get(this.industry).click({ force: true });
            cy.get(this.industry).type(`${ADPPayrollData.industry}{enter}`);
            cy.wait(2000);
            cy.get(this.industrydrop).contains(ADPPayrollData.industry).click({ force: true });
            cy.wait(2000);
            cy.get(this.businesstype).click({ force: true });
            cy.get(this.businesstypedrop).contains(ADPPayrollData.businesstypedrop).click({ force: true });
            cy.get(this.FilingFrequency).check({force:true});
            cy.get(this.DepositFrequency).check({force:true});
            cy.get(this.location).click({ force: true });
            cy.wait(3000)
            cy.get(this.location).type(`${ADPPayrollData.location}{enter}`);
            cy.get(this.street).type(ADPPayrollData.street);
            cy.get(this.city).type(ADPPayrollData.city);
            cy.get(this.state).click({ force: true });
            cy.wait(3000);
            cy.get(this.state).type(`${ADPPayrollData.state}{enter}`);
            cy.wait(2000);
            cy.get(this.statedrop).contains(ADPPayrollData.state).click({ force: true });
            cy.get(this.zipcode).type(ADPPayrollData.zipcode);
            cy.contains('Next').click({ force: true });
            cy.wait(3000);
            this.payrollSchedulePage(ADPPayrollData);
        }
        // Function to generate random 9 digit ein number
        randomEinNum() {
            var randomNum = "";
            var possible = "123456789";
            for (var i = 0; i < 9; i++)
                randomNum += possible.charAt(Math.floor(Math.random() * possible.length));
            return randomNum;
        }
    
    
        payrollSchedulePage(ADPPayrollData) {
            cy.get(this.payroll_frequency).check({force:true});
            cy.get(this.first_payment_date).type(ADPPayrollData.first_payment_date);
            cy.get(this.first_work_period_start_date).type(ADPPayrollData.first_work_period_start_date);
            cy.get(this.first_work_period_end_date).type(ADPPayrollData.first_work_period_end_date);
            cy.contains('Next').click({ force: true });
            cy.wait(4000);
            this.bankingInformationPage(ADPPayrollData);
    
          }
    
        bankingInformationPage(ADPPayrollData) {
            cy.contains('Connect Via Plaid').click({ force: true });
            cy.wait(2000);
            //cy.get('#aut-button').click();
            const iframe = '#plaid-link-iframe-1';
            const iframe2='#plaid-link-iframe-2';
             //Ensure the iframe is loaded
             cy.frameLoaded(iframe);
          // Switch context to the iframe and use cy.within() to scope subsequent commands
           cy.iframe(iframe).within(() => {
           // Find and click the element inside the iframe
           cy.contains('Continue').then($element => {
             if ($element.length) {
               // Element is present and clicked
               cy.wrap($element).click();
             }

           })
            cy.wait(3000)
            cy.get(this.link_account).eq(1).click();
            cy.get(this.Routing_number).type(ADPPayrollData.Routing_number);
            cy.get(this.continue).click();
            cy.get(this.account_number).type(ADPPayrollData.account_number);
            cy.get(this.confirm_account_number).type(ADPPayrollData.confirm_account_number,{ force: true });
            cy.get(this.continue).click();
            cy.get(this.full_name).type(ADPPayrollData.full_name);
            cy.get(this.continue).click();
            cy.wait(2000);
            cy.get(this.authorize).click();
            cy.get(this.Phone_number).type(ADPPayrollData.Phone_number,{ force: true });
            cy.get(this.continue).click({ force: true });
            cy.wait(2000)
            cy.get(this.continue).click({ force: true });
    
            })
            cy.wait(10000);
            cy.contains('Verify Bank Account').click({ force: true });
            //cy.get('button p.MuiTypography-root').click({ force: true });
            cy.wait(10000);
            cy.frameLoaded(iframe2);
            cy.iframe(iframe2).within(() => {
            cy.get(this.letter_code).type('ABC');
            cy.get(this.submit).click();
            cy.wait(5000);
            });
        
            cy.contains('Next').click({ force: true });
            cy.wait(3000);
            this .ownerDetails(ADPPayrollData);
    }

    ownerDetails(ADPPayrollData) {
        cy.get(this.first_name).type(ADPPayrollData.first_name);
        cy.get(this.last_name).type(ADPPayrollData.last_name);
        cy.get(this.email).type(ADPPayrollData.email);
        cy.get(this.dob).type(ADPPayrollData.dob);
        cy.get(this.phone).type(ADPPayrollData.phone);
        cy.get(this.owner_ssn).type(ADPPayrollData.owner_ssn);
        cy.get(this.gender).check({force:true});
        cy.get(this.is_owner_an_emplyee).check({force:true});
        cy.get(this.job_type).type(`${ADPPayrollData.job_type}{enter}`);
        cy.get(this.hire_date).type(ADPPayrollData.hire_date);
        cy.get(this.job_title).type(ADPPayrollData.job_title);
        cy.get(this.amount).type(ADPPayrollData.amount);
        cy.get(this.frequency).type(`${ADPPayrollData.frequency}{enter}`);
        cy.get(this.percentage).type(ADPPayrollData.percentage);
        cy.contains('Next').click({force:true});
        cy.wait(2000);
        this.agreements(ADPPayrollData);

    }

    agreements(ADPPayrollData){
        cy.get(this.agree1).check();
        cy.get(this.agree2).check();
        cy.get(this.agree3).check();
        cy.get(this.agree4).check();
        cy.get(this.agree5).check();
        cy.get(this.agree6).check();
        cy.get(this.agree7).check();
        cy.wait(2000);
        cy.get('canvas.sigCanvas').then($canvas => {
            const canvas = $canvas[0];
            const rect = canvas.getBoundingClientRect();
      
            // Define the coordinates for the click
            const clickX = rect.left + 30; // Adjust as needed
            const clickY = rect.top + 40; // Adjust as needed
      
            // Simulate mouse events to click inside the canvas
            cy.wrap(canvas)
            .click(clickX - rect.left, clickY - rect.top);
            });
        cy.wait(5000);
        cy.contains('Submit Payroll').click({force: true});
        cy.contains('Okay',{ timeout: 180000 }).click({force:true});

        
    }
    
}

export default adpPayroll