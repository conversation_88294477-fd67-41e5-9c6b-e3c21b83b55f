class UserPage {

    AddUser(userData) {
        cy.get('a[href="#/accountant/new"]').contains("Add User").click();
        cy.contains('label', 'First Name').parent().find('input').type(userData.firstName);
        cy.contains('label', 'Last Name').parent().find('input').type(userData.lastName);
        cy.contains('label', 'Email').parent().find('input').type(userData.email);

        cy.contains('label', 'Profile').parent().find('select').select(userData.profile);

        // Find the Accounting Skills section
        cy.contains('label', 'Accounting Skills')
            .closest('.form-group')
            .within(() => {
                // Open the dropdown
                cy.get('.select-title').click();

                const skillsToSelect = userData.accountingSkills;
                // Select specific skills
                skillsToSelect.forEach(skill => {
                    cy.contains('label', skill)
                        .find('input[type="checkbox"]')
                        .check({ force: true });
                });
            });


        cy.contains('label', 'Active').parent().find('input[type="checkbox"]').check({ force: true });
        cy.contains('label', 'Zone').parent().find('select').select(userData.zone);
        cy.contains('label', 'Team Role').parent().find('select').select(userData.teamRole);
        cy.contains('label', 'Time Zone').parent().find('select').select(userData.timeZone);

        cy.get('button[Type="submit"]').click({ force: true })
        cy.wait(4000)

    }


}

export default UserPage