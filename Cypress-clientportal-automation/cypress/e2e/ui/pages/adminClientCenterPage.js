
class ClientCenter {

    //Search Contact
    clientCenter = 'a[href="#/users"]'
    emailSearch = '.col-xs-3 .input-search'
    searchButton = 'span[class="items-search-btn"]'
    
    //Edit Contact
    button = '.panel-action-btn .btn-default'
    address = 'input[name="address"]'
    city = 'input[name="city"]'
    state = 'input[name="state"]'
    zip_code = 'input[name="zip_code"]'
    country = 'input[name="country"]'
    Save = 'button[type="submit"]'

    // Contact screen tab navigation
    contactScreen = 'ul[class="nav nav-pills ng-star-inserted"]'

    //AP creation
    addAP = 'button[class="btn btn-default"]'
    APOptions = '.select > .ng-pristine'
    createAP = 'div[class="modal-footer"]'
    APname = 'input[class="form-control ng-untouched ng-pristine ng-invalid"]'
    payrollSpeialist = '[label="Payroll Specialist"] > .form-group > .col-xs-3 > ng2-ui-typeahead > .placeholded > .typeahead > .form-control'
    specialistSearchResultList = 'button[id*="ngb-typeahead"]'
    addComment = 'textarea[formcontrolname="payroll_comments"]'
    saveAP = '.btn-success'

    contactSearchUsingEmailId(testUser) {
        cy.get(this.clientCenter).eq(0).click({ force: true })
        cy.log('uatTestUser: ' + testUser.uatTestUser)
        cy.log('stagingTestUser: ' + testUser.stagingTestUser)
        cy.log('prodTestUser: ' + testUser.prodTestUser)
       
        let currentUrl;
        cy.url().then((url) => {
            currentUrl = url;
            if (currentUrl.includes('uat')) {
                cy.get(this.emailSearch).type(testUser.uatTestUser)
            } else if (currentUrl.includes('staging')) {
                cy.get(this.emailSearch).type(testUser.stagingTestUser)
            } else {
                cy.get(this.emailSearch).type(testUser.prodTestUser)
            }
        });

        cy.get(this.searchButton).click({ force: true })
        cy.wait(2000)
        cy.get('tr td:nth-child(1)')
            .find('user-name[linktype="user"]')
            .should('be.visible')
            .scrollIntoView()
            .find('a[class="ng-star-inserted"]')
            .click({ force: true })
        cy.wait(2000)
    }

    //edit contact and add address to it
    editContact(testUser) {
        this.contactSearchUsingEmailId(testUser)
        cy.get(this.button).each(($el) => {
            if ($el.text() === 'Edit') {
                cy.wrap($el).click({ force: true });
            }
        })
        cy.get(this.address).clear().type('Test1')
        cy.get(this.city).clear().type('columbus')
        cy.get(this.state).clear().type('OH')
        cy.get(this.zip_code).clear().type('43004')
        cy.get(this.country).clear().type('USA')
        cy.get(this.Save).click()
        cy.wait(5000)
    }

    // Contact screen tab navigation
    contactScreenAllTabNavigation(testUser) {
        this.contactSearchUsingEmailId(testUser)
        cy.get(this.contactScreen).contains(' Related Accounts ').click({ force: true })
        cy.get(this.contactScreen).contains(' Client Intake ').click({ force: true })
        cy.get(this.contactScreen).contains(' ToS and PP ').click({ force: true })
        cy.get(this.contactScreen).contains(' Set Password ').click({ force: true })
        cy.get(this.contactScreen).contains(' Billing ').click({ force: true })
        cy.get(this.contactScreen).contains(' Tax Prep ').click({ force: true })
        cy.get(this.contactScreen).contains(' Tax Advisory ').click({ force: true })
        cy.get(this.contactScreen).contains(' Bookkeeping ').click({ force: true })
        cy.get(this.contactScreen).contains(' Payroll ').click({ force: true })
        cy.get(this.contactScreen).contains(' Sales and Use Tax ').click({ force: true })
        cy.get(this.contactScreen).contains(' Notice ').click({ force: true })
        cy.get(this.contactScreen).contains(' State Tax Filing ').click({ force: true })
        cy.get(this.contactScreen).contains(' 1099 Prep ').click({ force: true })
        cy.get(this.contactScreen).contains(' Extension ').click({ force: true })
        cy.get(this.contactScreen).contains(' Estimated Taxes ').click({ force: true })
        cy.get(this.contactScreen).contains(' Tax Savings Plan ').click({ force: true })
        cy.get(this.contactScreen).contains(' Entity Formation ').click({ force: true })
        cy.get(this.contactScreen).contains(' Vendasta ').click({ force: true })
    }

    payrollAPCreation(testUser) {
        this.contactSearchUsingEmailId(testUser)
        cy.get(this.addAP).contains('Add AP').click()
        cy.get(this.APOptions).select('2: payroll')
        cy.get(this.createAP).contains('Ok').click()
        cy.get(this.APname).type("Payroll AP")
        cy.get(this.payrollSpeialist).type('test').clear().type('Smith')

        cy.get(this.specialistSearchResultList).each(($el, index, $list) => {
            if ($el.text() === 'Shawn Smith') {
                cy.wrap($el).click()
            }
        })

        cy.get(this.addComment).type('test comment')
        cy.wait(5000)
        cy.get(this.saveAP).contains('Save').click({ force: true })
        cy.wait(4000)
    }

    // client portal login and navigation
    clientPortalLoginUsingAdminLogin(testUser) {
        this.contactSearchUsingEmailId(testUser)
        cy.get(this.button).each(($el) => {
            if ($el.text() === '1-800Accountant log in') {
                cy.wrap($el).invoke('removeAttr', 'target').click({ force: true });
            }
        })
    }
}
export default ClientCenter

