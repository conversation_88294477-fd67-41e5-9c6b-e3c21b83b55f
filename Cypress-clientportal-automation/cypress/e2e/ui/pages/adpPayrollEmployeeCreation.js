class adpEmployee {

    emp_first_name = '[data-cy="first_name"] input[placeholder="First Name (Required)"]'
    emp_last_name = '[data-cy="last_name"] input[placeholder="Last Name (Required)"]'
    emp_email = '[data-cy="email"] input[placeholder="Email (Required)"]'
    emp_date_of_birth = '[data-cy="dob"] input[name="dob"]'
    emp_ssn = '[data-cy="ssn"] input[name="ssn"]'
    emp_location='input[placeholder="Search Location"]'
    emp_street='input[name="street_1"]'
    emp_city='input[name="city"]'
    emp_state='#mui-component-select-state'
    emp_statedrop='ul li[data-value="FL"]'
    emp_zipcode='input[name="zip"]'
    emp_DOH='input[name="job_start_date"]'
    emp_jobrequired='.MuiFormControl-fullWidth #category-select-options'
    emp_jobrequireddrop='button .MuiButton-label'
    emp_jobtitle='input[name="job_title"]'
    emp_joblocation='#mui-component-select-job_location'
    emp_joblocatondrop='ul li[data-value="1b3dec5a-403c-4e27-b81c-48dacf8622d4"]'
    emp_compensation_amount='input[name="amount"]'
    emp_frequency='#mui-component-select-frequency'
    emp_frequencydrop='ul li[data-value="Hourly"]'
    emp_isowner='input[name="isOwner"][value="yes"]'
    emp_own_percentage='input[name="ownership"]'



    adpNewEmployee(ADPPayrollData) {
        // cy.wait(5000);
        // cy.contains('Payroll').click();
        cy.contains('Employees').click();
        cy.contains('New Employee').click()
        cy.get(this.emp_first_name).type(ADPPayrollData.emp_first_name);
        cy.get(this.emp_last_name ).type(ADPPayrollData.emp_last_name);
        cy.get(this.emp_email).type(ADPPayrollData.emp_email);
        cy.get(this.emp_date_of_birth).type(ADPPayrollData.emp_date_of_birth);
        cy.get(this.emp_ssn).type(this.uniqueSSN())
        cy.get(this.emp_location).type(`${ADPPayrollData.emp_location}{enter}`);
        cy.get(this.emp_street).type(ADPPayrollData.emp_street);
        cy.get(this.emp_city).type(ADPPayrollData.emp_city);
        cy.get(this.emp_state).click({ force: true });
        cy.get(this.emp_statedrop).click();
        cy.get(this.emp_zipcode).type(ADPPayrollData.emp_zipcode);
        cy.get(this.emp_DOH).type(ADPPayrollData.emp_DOH);
        cy.get(this.emp_jobrequired).click({force:true});
        cy.wait(3000)
        cy.get(this.emp_jobrequireddrop).contains('Create a New Job').click(); 
        cy.wait(3000)
        cy.get(this.emp_jobtitle).type(ADPPayrollData.emp_jobtitle);
        cy.get(this.emp_joblocation).click();
        cy.get(this.emp_joblocatondrop,{ timeout: 120000 }).click();
        cy.get(this.emp_compensation_amount).type(ADPPayrollData.emp_compensation_amount);
        cy.get(this.emp_frequency).click();
        cy.get(this.emp_frequencydrop).click();
        cy.get(this.emp_isowner).check({force:true});
        cy.get(this.emp_own_percentage).type(ADPPayrollData.emp_own_percentage);
        cy.wait(2000)
        cy.contains('Save').click({force:true});
        cy.wait(3000)
        
    }

    uniqueSSN() {
        var uniqueSSN = "";
        var possible = "012345678";
        for (var i = 0; i < 9; i++)
            uniqueSSN += possible.charAt(Math.floor(Math.random() * possible.length));
        return uniqueSSN;
    }

}
export default adpEmployee