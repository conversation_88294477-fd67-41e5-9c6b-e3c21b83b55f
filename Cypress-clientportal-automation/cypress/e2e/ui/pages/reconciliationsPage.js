import ManualbankPage from "./ManualbankPage";

class ReconciliationsPage {
  
    submitReconciliation()
    {   cy.wait(3000);
      cy.get('.MuiIconButton-label > .MuiSvgIcon-root').click();
        cy.wait(5000);
       // Click on Bookkepping option to expand all the module names showing in the left navigation panel
        cy.contains('Bookkeeping').click();
        cy.wait(3000)
       // Click on Reconciliations option to navigate to the Reconciliations page
        cy.contains('Reconciliations').click();
        cy.wait(5000)
       // If already existing reconciliation is showing in the list as reconciled transaction then unpublish and delete the reconciled transaction from the list and create a new one and submit it
        cy.get('body').then(($body) => {
          if ($body.text().includes('Reconciled')) {
            cy.wait(5000);
            cy.contains('Reconciled').click();
           // cy.wait(5000);
           // cy.get('.infinite-scroll-component > .MuiButtonBase-root').click();
            cy.wait(5000);
            // Click on unpublish button to unpublish the reconciled transaction and unpublished reconciliation will no longer appear in generated report
            cy.get('.MuiGrid-justify-xs-flex-end > div >:nth-child(1)').click();
            cy.wait(3000);
            cy.get('.MuiDialogContent-root> > div > :nth-child(2)').click();
            cy.wait(3000);
            // Verify created reconciliation unpublished successfully
            cy.get('.MuiAlert-message').should('have.text', 'Reconciliation has been unpublished successfully'); 
            // Click on the back arrow showing at the top of the reconcile page to navigate back to reconciliations details page
            cy.get('.infinite-scroll-component > .MuiButtonBase-root').click();
            cy.wait(3000);
            // Click on delete button to delete the reconciliation
            // Any progress you made on this reconciliation will be lost and This cannot be undone.
            cy.get('.MuiGrid-justify-xs-flex-end > div > :nth-child(1)').click();
            cy.get('.MuiDialogContent-root > >div >:nth-child(2)').click();
            // Verify reconciliation transaction deleted successfully
            cy.get('.MuiAlert-message').should('have.text', 'Reconciliation data has been deleted successfully');
          }
        
       // A brand new user is signed up and logged in and after logged in as a brand new user the bank account should be getting added first to perfrom the reconciliation, hence creating a Manual bank account first and then adding a reconciliation to it
          else if ($body.text().includes('Reconcile my Account')) {
            cy.contains('Reconcile my Account').click();
            cy.wait(5000);
            cy.contains('Create a Manual Account').click();
            //refer and reuse manual bank page code to create a manual bank account
            const bankfetch=new ManualbankPage();
            bankfetch.bankNameInput().type('Automated bank Account');
            bankfetch.bankType();
            bankfetch.amount().type('500');
            bankfetch.nextBtn();
            bankfetch.bankDetails();
            bankfetch.nextBtn();
            bankfetch.createAccount();
            cy.wait(3000);
            cy.contains('Reconciliation').click();
            cy.reload();
            cy.wait(3000);
            cy.contains('Reconcile').click();

          } 
          // If reconciliation is already submitted and the status is showing as InProgress,then unpublish and delete the Inprogress transaction from the list and create a new one and submit it
          else if($body.text().includes('Started')){
            cy.wait(3000);
            cy.contains('Reconcile').click();
            cy.wait(5000);
            cy.contains('button','Start New').click();
            cy.wait(3000);
            // Enter reconciliation end date
            cy.get(':nth-child(3) > :nth-child(1) > .MuiFormControl-root > .MuiInputBase-root > .MuiInputBase-input').click();
            cy.get(':nth-child(3) > .MuiIconButton-label > .MuiSvgIcon-root').click();
            cy.get('.MuiPickersCalendar-transitionContainer > :nth-child(1) > :nth-child(1) > :nth-child(7)').click();
            // Enter reconciliation ending balance
            cy.get(':nth-child(3) > :nth-child(2) > .MuiFormControl-root > .MuiInputBase-root > .MuiInputBase-input').type('0');
            // Click on Start now button to create the reconciliation
            cy.contains('Start Now').click();
            // Verify reconciliation created successfully
            cy.get('.MuiAlert-message').should('have.text', 'Reconciliation data created successfully');
            cy.wait(5000);
            // Select transaction from transaction list and click on edit button and make sure the difference should be zero before sunbmitting the reconciliation
            // To submit the reconciliation the difference between the starting and ending balance must be zero
            cy.get('.MuiListSubheader-root > :nth-child(1) > .MuiListItemIcon-root > .MuiButtonBase-root > .MuiIconButton-label').click();
            cy.contains('Edit').click();
            cy.get(':nth-child(3) > :nth-child(2) > .MuiFormControl-root > .MuiInputBase-root > .MuiInputBase-input').clear().type(500);
            // Click on save changes button to save the changes
            cy.get('[class*=MuiButton-contained]').eq(1).click();
            cy.wait(3000);
            // Select transaction from transaction list and the verify the difference is showing as Zero and click on save changes button
            cy.get('.MuiListItem-root > :nth-child(1) > .MuiButtonBase-root > .MuiIconButton-label').click();
            // Verify reconciliation data updated successfully
            cy.get('.MuiAlert-message').should('have.text', 'Reconciliation data updated successfully');
            cy.wait(3000);
            cy.get('.MuiButton-contained').click();
            cy.wait(3000);
            // Verify reconciliation transaction submitted successfully
            cy.get('.MuiAlert-message').should('have.text', 'Reconciliation has been submitted successfully');
            cy.wait(5000);
            cy.contains('Reconciled').click();
           // cy.wait(5000);
           // cy.get('.infinite-scroll-component > .MuiButtonBase-root').click();
            cy.wait(5000);
            // Click on unpublish button to unpublish the reconciled transaction and unpublished reconciliation will no longer appear in generated report
            cy.get('.MuiGrid-justify-xs-flex-end > div >:nth-child(1)').click();
            cy.wait(3000);
            cy.get('.MuiDialogContent-root> > div > :nth-child(2)').click();
            cy.wait(3000);
            // Verify created reconciliation unpublished successfully
            cy.get('.MuiAlert-message').should('have.text', 'Reconciliation has been unpublished successfully'); 
            // Click on the back arrow showing at the top of the reconcile page to navigate back to reconciliations details page
            cy.get('.infinite-scroll-component > .MuiButtonBase-root').click();
            cy.wait(3000);
            // Click on delete button to delete the reconciliation
            // Any progress you made on this reconciliation will be lost and This cannot be undone.
            cy.get('.MuiGrid-justify-xs-flex-end > div > :nth-child(1)').click();
            cy.get('.MuiDialogContent-root > >div >:nth-child(2)').click();
            // Verify reconciliation transaction deleted successfully
            cy.get('.MuiAlert-message').should('have.text', 'Reconciliation data has been deleted successfully');

          }
          
          //Click on Reconcile button showing on the Reconciliations page to perform the reconciliations actions such as create, submit, unpublish, edit and delete reconciliation
          if($body.text().includes('No Reconciliation Selected')) {
            // Click on Reconcile button
           cy.wait(5000); 
           //cy.get('.MuiBox-root > .MuiButtonBase-root').click();
           cy.contains('button','Reconcile').click();
           
          }
        })
          
        cy.wait(5000)
        // Enter reconciliation end date
        cy.get(':nth-child(3) > :nth-child(1) > .MuiFormControl-root > .MuiInputBase-root > .MuiInputBase-input').click();
        cy.get(':nth-child(3) > .MuiIconButton-label > .MuiSvgIcon-root').click();
        cy.get('.MuiPickersCalendar-transitionContainer > :nth-child(1) > :nth-child(1) > :nth-child(7)').click();
        // Enter reconciliation ending balance
        cy.get(':nth-child(3) > :nth-child(2) > .MuiFormControl-root > .MuiInputBase-root > .MuiInputBase-input').type('0');
        // Click on Start now button to create the reconciliation
        cy.contains('Start Now').click();
        // Verify reconciliation created successfully
        cy.get('.MuiAlert-message').should('have.text', 'Reconciliation data created successfully');
        cy.wait(5000);
        // Select transaction from transaction list and click on edit button and make sure the difference should be zero before sunbmitting the reconciliation
        // To submit the reconciliation the difference between the starting and ending balance must be zero
        cy.get('.MuiListSubheader-root > :nth-child(1) > .MuiListItemIcon-root > .MuiButtonBase-root > .MuiIconButton-label').click();
        cy.contains('Edit').click();
        cy.get(':nth-child(3) > :nth-child(2) > .MuiFormControl-root > .MuiInputBase-root > .MuiInputBase-input').clear().type(500);
        // Click on save changes button to save the changes
        cy.get('[class*=MuiButton-contained]').eq(1).click();
        cy.wait(3000);
        // Select transaction from transaction list and the verify the difference is showing as Zero and click on save changes button
        cy.get('.MuiListItem-root > :nth-child(1) > .MuiButtonBase-root > .MuiIconButton-label').click();
        // Verify reconciliation data updated successfully
        cy.get('.MuiAlert-message').should('have.text', 'Reconciliation data updated successfully');
        cy.wait(3000);
        cy.get('.MuiButton-contained').click();
        cy.wait(3000);
        // Verify reconciliation transaction submitted successfully
        cy.get('.MuiAlert-message').should('have.text', 'Reconciliation has been submitted successfully');
    }

    unpublishReconciliation(){
 
        // Select submitted reconciled transaction from the list
        cy.get('.infinite-scroll-component > .MuiButtonBase-root').click();
        cy.wait(5000);
        // Click on unpublish button to unpublish the reconciled transaction and unpublished reconciliation will no longer appear in generated report
        cy.get('.MuiGrid-justify-xs-flex-end > div >:nth-child(1)').click();
        cy.wait(3000);
        cy.get('.MuiDialogContent-root> > div > :nth-child(2)').click();
        cy.wait(3000);
        // Verify created reconciliation unpublished successfully
        cy.get('.MuiAlert-message').should('have.text', 'Reconciliation has been unpublished successfully');
    }

    editReconciliation(){
        // Select reconciled transaction from the list
        cy.get('.infinite-scroll-component > .MuiButtonBase-root').click();
        // Click on edit button to edit the reconciled transaction description and save changes 
        cy.get('.MuiGrid-justify-xs-flex-end > div > :nth-child(2)').click();
        cy.get('.MuiGrid-spacing-xs-1 > :nth-child(2) > .MuiFormControl-root > .MuiInputBase-root').type('Description edit reconcile');
        cy.contains('Save Changes').click();
        cy.wait(5000);
    }

    deleteReconciliation(){
        // Click on the back arrow showing at the top of the reconcile page to navigate back to reconciliations details page
        cy.get('.MuiBox-root > .MuiButtonBase-root').click();
        cy.get('.infinite-scroll-component > .MuiButtonBase-root').click();
        cy.wait(3000);
        // Click on delete button to delete the reconciliation
        // Any progress you made on this reconciliation will be lost and This cannot be undone.
        cy.get('.MuiGrid-justify-xs-flex-end > div > :nth-child(1)').click();
        cy.get('.MuiDialogContent-root > >div >:nth-child(2)').click();
        // Verify reconciliation transaction deleted successfully
        cy.get('.MuiAlert-message').should('have.text', 'Reconciliation data has been deleted successfully');


    }

  }


  export default ReconciliationsPage;