class mileageLog
{
    vehicles='[href="#/mileage/vehicles"]'
    activeVehicle='input[name="activeVehicle"]'
    defaultVehicle='input[name="defaultVehicle"]'
    vehicleMake='input[name="make"]'
    vehicleModel='input[name="model"]'
    vehicleYear='input[name="year"]'
    vehicleLicensePlate='input[name="licensePlate"]'
    vehicleMileageYear2023='input[name="mileageYear2023"]'
    vehicleMileageYear2022='input[name="mileageYear2022"]'
    vehicleVIN='input[name="vin"]'
    vehicleDescription='textarea[name="description"]'
    deleteVehicle='div.MuiDialog-paper .MuiButton-root'

    locationName='input[name="name"]'
    locationDescription='input[name="description"]'
    locationSearch='input[placeholder="Search Location"]'
    deleteLocation='div.MuiDialog-paper .MuiButton-root'


    purposeName='input[name="name"]'
    setAsDefaultPurpose='input[name="is_default_mt_purpose"]'
    savePurpose='div.MuiDialog-paper .MuiButton-root'
    deleteButton= 'div.MuiPaper-root .MuiButtonBase-root'
    deletePurpose='div.MuiDialog-paper .MuiButton-root'
    

    trips='[href="#/mileage/trips"]'
    vehicleDropdown='[data-cy="vehicle-input"]'
    vehicleList='div.MuiAutocomplete-popper'
    tripDate='input[name="date"]'
    startLocation='[data-cy="startLocation-input"]'
    destination='[data-cy="destination-input"]'
    locationList='div.MuiAutocomplete-popper'
    purpose='[data-cy="purpose-input"]'
    purposeList='div.MuiAutocomplete-popper'
    miles='input[name="miles"]'
    parking='input[name="parking"]'
    tolls='input[name="tolls"]'
    deleteTrip='div.MuiDialog-paper .MuiButton-root'
    

    //This method is for Vehicles page
    addEditDeleteVehicles()
    {
        //cy.get('[data-cy="expandable-sections-Mileage Log"]').click();
        cy.contains('Mileage Log').click()
        cy.wait(2000)
        cy.get(this.vehicles).click()
        cy.wait(2000)
        cy.contains('Add a Vehicle').click()
        cy.wait(2000)
        cy.get(this.activeVehicle).click()
        cy.wait(2000)
        cy.get(this.defaultVehicle).click()
        cy.wait(2000)
        cy.get(this.vehicleMake).type('BMW')
        cy.wait(2000)
        cy.get(this.vehicleModel).type('7')
        cy.wait(2000)
        cy.get(this.vehicleYear).type('2022')
        cy.wait(2000)
        cy.get(this.vehicleLicensePlate).type('1234EDOKvn#$%')
        cy.wait(2000)
        cy.get(this.vehicleMileageYear2023).type('23456')
        cy.wait(2000)
        cy.get(this.vehicleMileageYear2022).type('67890')
        cy.wait(2000)
        cy.contains('Save Vehicle').click()
        cy.wait(3000)
        cy.contains('Edit').click()
        cy.wait(3000)
        cy.get(this.vehicleVIN).type('67890abc123')
        cy.wait(3000)
        cy.get(this.vehicleDescription).type('Description added')
        cy.wait(3000)
        cy.contains('Save Changes').click({ force: true })
        cy.wait(3000)
        cy.contains('Delete').click()
        cy.wait(3000)
        cy.get(this.deleteVehicle).contains('Delete').click()
        cy.wait(4000)

    }


    //This method is for Locations page
    addEditDeleteLocations()
    {
        //cy.contains('Mileage Log').click()     //For running individual page
        //cy.wait(3000)
        cy.contains('Locations').click()
        cy.wait(3000)
        cy.wait(3000)
        cy.contains('Add Location').click()                             
        cy.wait(2000)
        cy.get(this.locationName).type('Location 1')
        cy.wait(2000)
        cy.get(this.locationDescription).type('Use this as default location')
        cy.wait(2000)
        cy.get(this.locationSearch).type(' Washington City')
        cy.wait(2000)
        cy.contains('Save Location').click({ force: true })
        cy.wait(4000)
        cy.contains('Edit').click()
        cy.wait(3000)
        cy.get(this.locationName).type(' Edited')
        cy.wait(2000)
        cy.get(this.locationDescription).type(' edited')
        cy.wait(2000)
        cy.contains('Save Location').click()
        cy.wait(3000)
        cy.contains('Delete').click()
        cy.wait(3000)
        cy.get(this.deleteLocation).contains('Delete').click();
        cy.wait(2000)
    }


    //This method is for Purposes page
    addEditDeletePurposes()
    {
        //cy.contains('Mileage Log').click()      //For running individual page
        //cy.wait(3000)
        cy.contains('Purposes').click()
        cy.wait(3000)
        cy.contains('New Purpose').click()
        cy.wait(3000)
        cy.get(this.purposeName).type('Manual Added Purpose 1')
        cy.wait(3000)
        cy.get(this.setAsDefaultPurpose).click()
        cy.wait(3000)
        cy.get(this.savePurpose).contains('Save').click()
        cy.wait(7000)
        cy.get('#purpose-661f80f4f5d4aa0ba85477d2').click()
        cy.wait(3000)
        cy.contains('Edit').click()
        cy.wait(3000)
        cy.get(this.purposeName).type(' Edited')
        cy.wait(3000)
        cy.get(this.setAsDefaultPurpose).click()
        cy.wait(3000)
        cy.get(this.savePurpose).contains('Save').click()
        cy.wait(7000)
        cy.get('#purpose-662214e6411ee36f2838c0d2').click()
        cy.wait(3000)
        cy.get(this.deleteButton).contains('Delete').click()
        cy.wait(3000)
        cy.get(this.deletePurpose).contains('Delete').click()
        cy.wait(3000)


    }


    //This method is for Trips page
    addEditDeleteTrips()
    {
        //cy.contains('Mileage Log').click()
        //cy.wait(3000)
        //cy.get(this.trips).click()
        cy.contains('Trips').click()
        cy.wait(4000)
        //cy.contains('Add Your First Trip').click()
        cy.contains('New Trip').click()
        cy.wait(3000)
        cy.get(this.vehicleDropdown).click()
        cy.wait(2000)
        cy.get(this.vehicleList).contains('Benz GL 5').click({ force: true })
        cy.wait(2000)
        cy.get(this.startLocation).click()
        cy.wait(2000)
        cy.get(this.locationList).contains('California 1').click({ force: true })
        cy.wait(2000)
        cy.get(this.destination).click()
        cy.wait(2000)
        cy.get(this.locationList).contains('LA').click({ force: true })
        cy.wait(2000)
        cy.get(this.purpose).click()
        cy.wait(2000)
        cy.get(this.purposeList).contains('Commute').click({ force: true })
        cy.wait(2000)
        cy.get(this.miles).type('22')
        cy.wait(2000)
        cy.get(this.parking).type('4')
        cy.wait(2000)
        cy.get(this.tolls).type('2')
        cy.wait(2000)
        cy.contains('Save Trip').click()
        cy.wait(4000)
        cy.contains('22 Mi').click()
        cy.wait(2000)
        cy.contains('Edit').click()
        cy.wait(3000)
        cy.get(this.purpose).click()
        cy.wait(3000)
        cy.get(this.purposeList).contains('Between Offices').click({ force: true })
        cy.wait(2000)
        cy.get(this.parking).type('5')
        cy.wait(2000)
        cy.contains('Save Changes').click()
        cy.wait(4000)
        cy.contains('Delete').click()
        cy.wait(2000)
        cy.get(this.deleteTrip).contains('Delete').click()
        cy.wait(2000)


    }




}
export default mileageLog