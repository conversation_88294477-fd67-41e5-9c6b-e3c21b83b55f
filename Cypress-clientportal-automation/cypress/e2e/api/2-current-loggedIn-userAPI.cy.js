describe("Test Scenario: Validate Current Logged In User API", () => {
  const userDetailsUrl = "cbapi/app.php/accountant_api/accountant/current";

  before(() => {
    // Login once and set cookie
    cy.setupAdminSession();
  });

  it("Test Case 01: Should return current user details after login", () => {
    cy.api({
      method: "GET",
      url: userDetailsUrl,
      headers: {
        accept: "application/json",
        "x-timezone-offset": "-330",
      },
    }).then((response) => {
      expect(response.status).to.eq(200);

      const body = response.body;
      expect(body.username).to.eq("<EMAIL>");
      expect(body.is_admin).to.eq(true);
      expect(body.accounting_skills).to.be.an("array");

      expect(body.id).to.be.a("string").and.have.length.greaterThan(10);
      expect(body.accounting_skills).to.be.an("array");
      expect(body.alias).to.be.a("string");
      expect(body.cp_access).to.be.true;
      expect(body.active).to.be.a("boolean");
    });
  });
});
