describe("Test Scenario: Validate Current Logged In User API", () => {
  it("Test Case01: Validate permissions using accountant_id from current user", () => {
    cy.setupAdminSession();

    let accountantId;

    cy.api({
      method: "GET",
      url: "cbapi/app.php/accountant_api/accountant/current",
      headers: {
        accept: "application/json",
        "x-timezone-offset": "-330",
      },
    })
      .then((response) => {
        expect(response.status).to.eq(200);
        accountantId = response.body.id;
        expect(accountantId).to.match(/^[a-f0-9]{24}$/);
      })
      .then(() => {
        cy.api({
          method: "GET",
          url: `cbapi/app.php/admin_api/access_control/permissions?accountant_id=${accountantId}`,
          headers: {
            accept: "application/json",
            "x-timezone-offset": "-330",
          },
        }).then((response) => {
          expect(response.status).to.eq(200);
          expect(response.body).to.have.property("access_level", "ADMIN");

          const permissionFields = [
            "dashboard_permissions",
            "client_center_permissions",
            "calendar_permissions",
            "message_center_permissions",
            "mass_messaging_permissions",
            "documents_permissions",
            "tax_advisory_permissions",
            "tax_center_tax_prep_ap_permissions",
            "tax_center_other_ap_permissions",
            "bookkeeping_permissions",
            "payroll_permissions",
            "entity_department_permissions",
            "cases_permissions",
            "users_permissions",
            "rabt_permissions",
            "manager_view_permissions",
            "reports_permissions",
            "ip_blocking_permissions",
          ];

          permissionFields.forEach((field) => {
            expect(response.body).to.have.property(field);
            expect(response.body[field])
              .to.be.a("number")
              .and.to.be.within(1, 3);
          });
        });
      });
  });
});
