describe("Test Scenario: End to End flow to Test Login API Feature", () => {
  const baseUrl = "cbapi/app.php/accountant_login";

  /**
   * Test Case 01
   * Verifies that valid credentials return a 200 status code,
   * success flag as true, and a valid non-empty session ID.
   */
  it("Test Case 01: Valid username and password should return 200 and session ID", () => {
    cy.fixture("loginCredentials.json").then((credentials) => {
      cy.api({
        method: "POST",
        url: baseUrl,
        body: {
          username: credentials.username,
          password: credentials.password,
        },
      }).then((response) => {
        expect(response.status).to.eq(200); // Success status
        expect(response.body.success).to.eq(true); // success must be true
        expect(response.body.session_id).to.be.a("string").and.not.to.be.empty; // session ID should be valid
      });
    });
  });

  /**
   * Test Case 02
   * Checks that an incorrect username with a valid password returns a 401 Unauthorized status.
   */
  it("Test Case 02: Invalid username should return 401 and success false", () => {
    cy.api({
      method: "POST",
      url: baseUrl,
      body: {
        username: "<EMAIL>", // Invalid username
        password: "1800acc@Stage",        // Valid password
      },
      failOnStatusCode: false,
    }).then((response) => {
      expect(response.status).to.eq(401); // Unauthorized
    });
  });

  /**
   * Test Case 03
   * Checks that a valid username with an incorrect password returns a 401 Unauthorized status.
   */
  it("Test Case 03: Invalid password should return 401 and success false", () => {
    cy.api({
      method: "POST",
      url: baseUrl,
      body: {
        username: "<EMAIL>", // Valid username
        password: "wrongPassword",       // Invalid password
      },
      failOnStatusCode: false,
    }).then((response) => {
      expect(response.status).to.eq(401); // Unauthorized
    });
  });

  /**
   * Test Case 04
   * Ensures that sending a POST request without a request body returns a 400 Bad Request.
   */
  it("Test Case 04: Missing payload should return 400 and success false", () => {
    cy.api({
      method: "POST",
      url: baseUrl,
      failOnStatusCode: false,
    }).then((response) => {
      expect(response.status).to.eq(400); // Bad Request
    });
  });

  /**
   * Test Case 05
   * Verifies how the system handles case sensitivity in the username.
   * (Expect 200 if case-insensitive, 401 if case-sensitive)
   */
  it("Test Case 05: Case sensitivity check on username", () => {
    cy.api({
      method: "POST",
      url: baseUrl,
      body: {
        username: "<EMAIL>", // Uppercase username
        password: "1800acc@Stage",
      },
      failOnStatusCode: false,
    }).then((response) => {
      expect(response.status).to.eq(200); // Adjust to 401 if your system is case-sensitive
    });
  });

  /**
   * Test Case 06
   * Verifies that the returned session_id follows a valid format (alphanumeric, at least 10 characters).
   */
  it("Test Case 06: Session ID should match expected format", () => {
    cy.fixture("loginCredentials.json").then((credentials) => {
      cy.api({
        method: "POST",
        url: baseUrl,
        body: {
          username: credentials.username,
          password: credentials.password,
        },
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.session_id).to.match(/^[a-z0-9]{10,}$/); // Customize regex as needed
      });
    });
  });
});