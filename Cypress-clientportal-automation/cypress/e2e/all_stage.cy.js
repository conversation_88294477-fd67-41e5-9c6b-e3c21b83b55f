// Core login and navigation tests
import './ui/tests/TC_adminPortalLoginStage.cy'
import './ui/tests/TC_contactScreenAllTabNavigation.cy'
import './ui/tests/TC_clientPortalLoginUsingAdminUser.cy'
import './ui/tests/TC_allAdminPageNavigations.cy'
import './ui/tests/TC_mainNavigation.cy'

// Account and contact management tests
import './ui/tests/TC_addEditDeleteContacts.cy'
import './ui/tests/TC_editContact.cy'
import './ui/tests/TC_adminContactAndAccountSearch.cy'
import './ui/tests/TC_addNewBusinessForanExistingUser.cy'

// Financial management tests
import './ui/tests/TC_addEditDeleteTransactions.cy'
import './ui/tests/TC_addEditMoveDeleteCategory.cy'
import './ui/tests/TC_AddUpdateDeleteInvoices.cy'
import './ui/tests/TC_addConnectedBankAccount.cy'
import './ui/tests/TC_addManualBankAccount.cy'
import './ui/tests/TC_reconciliations.cy'
import './ui/tests/TC_submitUnpublishEditDeleteReconciliations.cy'

// Products and services tests
import './ui/tests/TC_addEditDeleteProductsServices.cy'

// Reporting tests
import './ui/tests/TC_generateAllReports.cy'

// Journal and accounting tests
import './ui/tests/TC_journalEntry.cy'

// Contractor and 1099 tests
import './ui/tests/TC_contractorCreationAnd1099Filing.cy'

// Payroll tests
import './ui/tests/TC_payrollSetup.cy'
import './ui/tests/TC_PayrollSetupADP.cy'
import './ui/tests/TC_Payroll_Appointment_schedule_Using_TeamModule.cy'

// Tax center tests
import './ui/tests/TC_taxCenterPageAllFunctionality.cy'

// Document management tests
import './ui/tests/TC_documentsUpload.cy'
import './ui/tests/TC_documentUploadInMessage.cy'
import './ui/tests/TC_receiptScan.cy'

// Communication tests
import './ui/tests/TC_startNewConversation.cy'
import './ui/tests/TC_replyToAnExistingMassageThreadFromClientSide.cy'
import './ui/tests/TC_viewArchivedMesg.cy'
import './ui/tests/TC_emailer.cy'

// Mileage and expense tests
import './ui/tests/TC_mileageLog.cy'

// VTO tests
import './ui/tests/TC_businessVto.cy'
import './ui/tests/TC_personalVto.cy'

// Additional functionality tests
import './ui/tests/TC_additionalProduct.cy'
import './ui/tests/TC_upgradeClientTieredPermission.cy'
import './ui/tests/TC_accountantCreation.cy'

// AP creation tests
import './ui/tests/TC_APcreationOnClientCenterPage.cy'

