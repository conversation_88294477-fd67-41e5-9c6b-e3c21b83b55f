{"name": "1800regressionsuite-qa", "version": "1.0.0", "description": "Cypress E2E Test Suite for 1800Accountant Portal", "main": "cypress.config.js", "repository": {"type": "git", "url": "."}, "scripts": {"uatSanityOnBrowser": "npx cypress run --spec \"cypress/e2e/all_uat.cy.js\" --headed", "uatSanity": "npx cypress run --spec \"cypress/e2e/all_uat.cy.js\"", "stageSanityOnBrowser": "npx cypress run --spec \"cypress/e2e/all_stage.cy.js\" --headed", "stageSanity": "npx cypress run --spec \"cypress/e2e/all_stage.cy.js\"", "test:open": "npx cypress open", "test:api": "npx cypress run --spec \"cypress/e2e/api/*.cy.js\"", "test:ui": "npx cypress run --spec \"cypress/e2e/ui/tests/*.cy.js\"", "test:smoke": "npm run uatSanity", "test:regression": "npx cypress run --spec \"cypress/e2e/ui/tests/*.cy.js\"", "clean:reports": "rm -rf cypress/reports cypress/screenshots cypress/videos", "pretest": "npm run clean:reports"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"chai-json-schema": "^1.5.1", "cypress": "^14.4.1", "cypress-mochawesome-reporter": "^3.8.2", "cypress-plugin-api": "^2.11.2", "cypress-xpath": "^2.0.1", "faker": "^6.6.6"}}