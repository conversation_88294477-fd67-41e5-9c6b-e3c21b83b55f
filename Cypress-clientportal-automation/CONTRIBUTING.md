# Contributing to 1800Accountant E2E Test Suite

Thank you for contributing to our test automation project! This guide will help you get started.

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- Git
- Basic understanding of Cypress and JavaScript

### Setup
1. Fork the repository
2. Clone your fork: `git clone <your-fork-url>`
3. Install dependencies: `npm install`
4. Create a feature branch: `git checkout -b feature/your-feature-name`

## 📝 Writing Tests

### Test Structure
Follow this structure for new test files:

```javascript
import PageObject from '../pages/PageObject';

describe('Feature Name', () => {
  before(() => {
    cy.fixture('testData').then(data => {
      this.testData = data;
    });
  });

  beforeEach(() => {
    cy.restoreLocalStorage();
  });

  it('should perform specific action', function() {
    const page = new PageObject();
    
    // Arrange
    page.navigateToPage();
    
    // Act
    page.performAction(this.testData);
    
    // Assert
    page.verifyResult();
  });

  afterEach(() => {
    cy.saveLocalStorage();
  });
});
```

### Page Object Model
Create page objects in `cypress/e2e/ui/pages/`:

```javascript
class PageName {
  // Selectors
  elementSelector = '[data-cy="element"]';
  
  // Methods
  performAction(data) {
    cy.get(this.elementSelector).click();
    return this;
  }
  
  verifyResult() {
    cy.get(this.elementSelector).should('be.visible');
    return this;
  }
}

export default PageName;
```

### Naming Conventions
- **Test files**: `TC_featureName.cy.js`
- **Page objects**: `featureNamePage.js`
- **Fixtures**: `featureData.json`
- **Methods**: Use camelCase, be descriptive

### Test Data
Store test data in `cypress/fixtures/`:

```json
{
  "validUser": {
    "username": "<EMAIL>",
    "password": "testPassword"
  },
  "invalidUser": {
    "username": "<EMAIL>",
    "password": "wrongPassword"
  }
}
```

## 🔍 Code Review Guidelines

### Before Submitting
- [ ] Tests pass locally
- [ ] Code follows existing patterns
- [ ] Page objects are used appropriately
- [ ] Test data is in fixtures
- [ ] Meaningful test descriptions
- [ ] No hard-coded waits

### Review Checklist
- [ ] Tests are independent and atomic
- [ ] Proper error handling
- [ ] Consistent naming conventions
- [ ] Documentation updated if needed
- [ ] No sensitive data in code

## 🐛 Bug Reports

When reporting bugs, include:
- Test file and line number
- Expected vs actual behavior
- Steps to reproduce
- Environment details
- Screenshots/videos if applicable

## 📋 Pull Request Process

1. **Create descriptive PR title**
   - ✅ "Add: Login functionality tests"
   - ❌ "Update tests"

2. **Include description**
   - What was changed
   - Why it was changed
   - How to test the changes

3. **Link related issues**
   - Use "Fixes #123" or "Closes #123"

4. **Request review**
   - Tag relevant team members
   - Wait for approval before merging

## 🏷️ Commit Message Format

Use conventional commits:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New test or feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `refactor`: Code refactoring
- `test`: Adding missing tests
- `chore`: Maintenance tasks

**Examples:**
- `feat(auth): add two-factor authentication tests`
- `fix(login): resolve timeout issue in admin login`
- `docs(readme): update installation instructions`

## 🚫 What Not to Do

- Don't commit sensitive data (passwords, API keys)
- Don't use hard-coded waits (`cy.wait(5000)`)
- Don't test third-party functionality
- Don't create overly complex tests
- Don't ignore test failures

## 🎯 Best Practices

### Test Design
- One assertion per test when possible
- Use descriptive test names
- Keep tests independent
- Use data-driven testing

### Code Quality
- Follow existing code style
- Use meaningful variable names
- Add comments for complex logic
- Keep methods small and focused

### Performance
- Avoid unnecessary API calls
- Use session management efficiently
- Consider test execution time
- Group related tests logically

Thank you for helping improve our test automation! 🚀
